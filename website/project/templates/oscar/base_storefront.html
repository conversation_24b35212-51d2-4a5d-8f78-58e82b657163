{% load i18n compress %}
{% load static %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE|default:"en-gb" }}" class="no-js">
    <head>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
        
        <!-- Preconnect to required origins (lower priority) -->
        <link rel="preconnect" href="https://consent.cookiefirst.com">
        <link rel="preconnect" href="https://www.googletagmanager.com">

        <!-- DNS prefetch for potential media domains -->
        <link rel="dns-prefetch" href="//{{ request.get_host }}">
        
        <!-- Montserrat Variable Font - Single file, all weights (~207KB, no blocking) -->
        {% compress css inline %}
        <style>
        @font-face {
            font-family: 'Montserrat';
            src: url('{{ STATIC_URL }}fonts/webfonts/Montserrat[wght].woff2') format('woff2-variations');
            font-weight: 100 900;
            font-style: normal;
            font-display: swap;
        }
        
        /* Fallback font adjustments to match Montserrat metrics better */
        @font-face {
            font-family: 'Montserrat Fallback';
            src: local('Segoe UI'), local('Roboto'), local('Open Sans'), local('Helvetica Neue'), local('Arial');
            size-adjust: 95%; /* Slightly smaller to match Montserrat */
            ascent-override: 90%;
            descent-override: 22%;
            line-gap-override: 0%;
        }
        </style>
        {% endcompress %}

        <!-- Preload critical resources (lower priority than LCP images) -->
        <link rel="preload" href="{{ STATIC_URL }}fonts/webfonts/Montserrat[wght].woff2" as="font" type="font/woff2" crossorigin="anonymous" fetchpriority="low">
        <link rel="preload" href="https://consent.cookiefirst.com/sites/partan.eu-5bb4add4-78a1-4b48-b825-0812cd36dff1/consent.js" as="script" fetchpriority="low">
        <link rel="preload" href="https://www.googletagmanager.com/gtm.js?id=GTM-NVTKG86J" as="script" fetchpriority="low">

        <!-- CookieFirst Consent Mode Defaults -->
        <script>
            // Define dataLayer and the gtag function
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            
            // Default consent states for EU regions
            gtag('consent', 'default', {
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied',
                'personalization_storage': 'denied',
                'analytics_storage': 'denied',
                'functionality_storage': 'denied',
                'security_storage': 'granted',
                'wait_for_update': 2000,
                'region': ['AT', 'BE', 'BG', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IS', 'IE', 'IT', 'LV', 'LI', 'LT', 'LU', 'MT', 'NL', 'NO', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'CH', 'GB']
            }); 
            // Default settings for all users out of EU + UK
            gtag('consent', 'default', {
                'ad_storage': 'granted',
                'ad_user_data': 'granted',
                'ad_personalization': 'granted',
                  'personalization_storage': 'denied',
                'analytics_storage': 'granted',
                'functionality_storage': 'granted',
                'security_storage': 'granted'
            });
        </script>
    
        <!-- CookieFirst Banner -->
        <script async src="https://consent.cookiefirst.com/sites/partan.eu-5bb4add4-78a1-4b48-b825-0812cd36dff1/consent.js"></script>
    
        <!-- Google Tag Manager -->
        <script>
            // Initialize GTM after consent defaults are set
            dataLayer.push({'gtm.start': new Date().getTime(), event:'gtm.js'});
        </script>
        <script async src="https://www.googletagmanager.com/gtm.js?id=GTM-NVTKG86J"></script>

        <!-- Structured Data -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "OnlineStore",
            "@id": "https://www.partan.eu",
            "name": "PARTAN Group",
            "url": "https://{{ request.get_host }}/",
            "logo": "https://{{ request.get_host }}/static/img/partan_head.png",
            "hasOfferCatalog": {
                "@type": "OfferCatalog",
                "numberOfItems": 109245
            },
            "areaServed": {
                "@type": "GeoCircle",
                "geoMidpoint": {
                    "@type": "GeoCoordinates",
                    "latitude": "54.8985",
                    "longitude": "23.9036"
                },
                "geoRadius": "20000000"
            },
            "sameAs": [
                "https://www.partan.eu",
                "https://www.partan.de",
                "https://www.partan.lt",
                "https://www.partan.pl",
                "https://www.partan24.es",
                "https://www.partan.fr"
            ],
            "subOrganization": [
                {
                    "@type": "AutoPartsStore",
                    "name": "PABALTIJO AUTOMOBILIŲ PREKYBA",
                    "address": {
                        "@type": "PostalAddress",
                        "streetAddress": "Švenčionių g. 2c",
                        "addressLocality": "Kaunas",
                        "postalCode": "45174",
                        "addressCountry": "LT"
                    },
                    "telephone": "+37067327008",
                    "email": "<EMAIL>",
                    "areaServed": "LT"
                },
                {
                    "@type": "OnlineStore",
                    "name": "PARTAN GMBH",
                    "address": {
                        "@type": "PostalAddress",
                        "streetAddress": "Hansastraße 66B",
                        "addressLocality": "Ronnenberg",
                        "postalCode": "30952",
                        "addressCountry": "DE"
                    },
                    "telephone": "+4917660331582",
                    "email": "<EMAIL>",
                    "areaServed": ["DE", "AT", "CH"]
                }
            ],
            "contactPoint": [
                {
                    "@type": "ContactPoint",
                    "telephone": ["+48570730535", "+48717776688"],
                    "contactType": "sales",
                    "areaServed": "PL",
                    "availableLanguage": "pl"
                },
                {
                    "@type": "ContactPoint",
                    "telephone": "+33970019368",
                    "contactType": "sales",
                    "areaServed": "FR",
                    "availableLanguage": "fr"
                },
                {
                    "@type": "ContactPoint",
                    "telephone": "+34677142935",
                    "contactType": "sales",
                    "areaServed": "ES",
                    "availableLanguage": "es"
                }
            ]
        }
        </script>
        <!-- End Structured Data -->

        <title>{% block title %}{{ shop_name }} | {{ shop_tagline }}{% endblock %}</title>
        <meta name="created" content="{% now "jS M Y h:i" %}" />
        <meta name="description" content="{% block description %}{{ shop_description }}{% endblock %}" />
        <meta name="keywords" content="{% block keywords %}{{ shop_keywords }}{% endblock %}" />
        <meta name="viewport" content="{% block viewport %}width=device-width, initial-scale=1, shrink-to-fit=no{% endblock %}">

        <style>
            [x-cloak] { display: none !important; }
        </style>

        {% block mainstyles %}
            {% compress css %}
                <link rel="stylesheet" href="{% static 'tailwind/output.css' %}">
            {% endcompress %}
        {% endblock %}

        {% if debug %}  
        <!-- DOM Analyzer -->
            {% compress js %}
                <script src="{{ STATIC_URL }}js/dom-analyzer.js"></script>
            {% endcompress %}
        {% endif %}

        {% block open_graph %}{% endblock %}
        {% block robots %}{% endblock %}

        {% block favicon %}
            <link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}" />
            <link rel="apple-touch-icon" href="{% static 'favicon.ico' %}" />
        {% endblock %}

        {% block alternate_languages %}
            {% if alt_paths %}
                {# Add canonical link #}
                {% for alt_path in alt_paths %}
                    {% if alt_path.is_canonical %}
                        <link rel="canonical" href="{{ alt_path.path }}" />
                    {% endif %}
                {% endfor %}

                {# Add hreflang tags #}
                {% for alt_path in alt_paths %}
                    {% if not alt_path.is_canonical %}
                        <link rel="alternate" hreflang="{{ alt_path.lang_code }}" href="{{ alt_path.path }}" />
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endblock %}

        {# Additional CSS - specific to certain pages #}
        {% block extrastyles %}
        {% endblock %}

        {% block extrahead %}{% endblock %}
    </head>

    <body id="{% block body_id %}default{% endblock %}" class="{% block body_class %}default{% endblock %}">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NVTKG86J"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
        {# Main content goes in this 'layout' block #}
        {% block layout %}{% endblock %}

        {% block tracking %}
            {# Default to using Google analytics #}

        {% endblock %}

        {% comment %}
        Scripts loaded from a CDN.  These can't be wrapped by the 'compress' tag and so we
        use a separate block for them.
        {% endcomment %}
        {% block cdn_scripts %}
        {% endblock %}

        {# Local scripts #}
        {% block scripts %}
        {% endblock %}

        {# Additional JS scripts #}
        {% block extrascripts %}
        {% endblock %}

        <!-- Global script for robot-hidden links -->
        <script>
        // Enable JavaScript-only content immediately
        document.documentElement.classList.add('js-enabled');

        document.addEventListener('DOMContentLoaded', function() {
            // Handle robot-hidden links globally
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-action]');
                if (!target) return;

                const action = target.getAttribute('data-action');
                const productId = target.getAttribute('data-product-id');

                if (action === 'price-offer') {
                    // Construct URL dynamically to avoid robots
                    window.location.href = '/catalogue/products/' + productId + '/price-offer/';
                } else if (action === 'contact-vendor') {
                    // Construct URL dynamically to avoid robots
                    window.location.href = '/catalogue/products/' + productId + '/contact-vendor/';
                }
            });
        });
        </script>

        {% compress js %}
            <script defer src="{% static 'js/htmx.min.js' %}"></script>
            <script defer src="{% static 'js/alpine.min.js' %}"></script>
        {% endcompress %}
    </body>
</html>
