
{% load basket_tags %}
{% load i18n %}
{% load custom_purchase_info_tags %}

{% purchase_info_for_product request product as session %}

{% if session.availability.is_available_to_buy %}
    {% if not product.is_full_car %}
        {% basket_form request product as basket_form %}
        <form action="{% url 'basket:add' pk=product.pk %}" method="post">
            {% csrf_token %}
            <input name="product_id" value="{{ product.id }}" type="hidden">
            <input name="quantity" value="1" type="hidden">
            <button type="submit" class="product_by_now">{% trans "Buy now" %}</button>
        </form>
    {% else %}
        <span class="product_by_now cursor-pointer" data-action="contact-vendor" data-product-id="{{ product.id }}">{% trans 'Contact vendor' %}</span>
    {% endif %}
{% else %}
    <button class="product_by_now">{% trans "Buy now" %}</button>
{% endif %}
