
{% load basket_tags %}
{% load i18n %}
{% load custom_purchase_info_tags %}

{% purchase_info_for_product request product as session %}

{% if session.availability.is_available_to_buy %}
    {% if not product.is_full_car and product.active %}
        {% basket_form request product as basket_form %}
        <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="form-stacked add-to-basket">
            {% csrf_token %}
            {% include "oscar/partials/form_fields.html" with form=basket_form %}
            <button type="submit" class="btn btn-large btn-success btn-block" value="{% trans 'Buy now' %}">{% trans "Buy now" %}</button>
        </form>
    {% else %}
        <div class="row">
            <div class="col-lg-6">
                <span class="btn btn-large btn-success btn-block cursor-pointer" data-action="contact-vendor" data-product-id="{{ product.id }}">{% trans 'Contact vendor' %}</span>
            </div>
        </div>
    {% endif %}
{% else %}
    {% if has_active_alert %}
        <p>{% trans "You have an active stock alert for this product." %}</p>
    {% else %}
        <form id="alert_form" method="post" action="{% url 'customer:alert-create' pk=product.id %}" class="add-to-basket">
            {% csrf_token %}
            <p>{% trans "You can get an email alert when this product is back in stock." %}</p>
            {% include "oscar/partials/form_fields.html" with form=alert_form %}
            <button type="submit" class="btn btn-large btn-success btn-block">{% trans "Notify me" %}</button>
        </form>
    {% endif %}
{% endif %}
