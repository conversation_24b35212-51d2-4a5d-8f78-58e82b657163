{% load custom_currency_filters %}
{% load i18n %}

{% comment %}
Use purchase_info from context instead of making additional DB queries.
The purchase_info is already calculated in the view to avoid duplicate queries.
{% endcomment %}
{% if purchase_info.price.exists %}
    {% if purchase_info.price.is_tax_known %}
        {{ purchase_info.price.incl_tax|user_currency:user_currency }}
    {% else %}
        {{ purchase_info.price.excl_tax|user_currency:user_currency }}
    {% endif %}
{% endif %}
