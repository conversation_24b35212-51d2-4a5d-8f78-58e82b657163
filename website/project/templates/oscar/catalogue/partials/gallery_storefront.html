{% load image_tags %}
{% load i18n %}
{% load custom_purchase_info_tags %}
{% load static %}

{% purchase_info_for_product request product as session %}
{% with all_images=product.images.all %}

<div class="product-gallery" x-data="productGallery()" x-init="init()">
    {% if all_images|length > 1 %}
        <!-- Main Image Display -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden mb-4">
            <div class="aspect-w-4 aspect-h-3">
                <!-- CRITICAL: Server-side render ONLY first image immediately for LCP -->
                {% with first_image=all_images.0 %}
                    {% oscar_thumbnail first_image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}
                    {% oscar_thumbnail first_image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}
                    {% oscar_thumbnail first_image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                    <div class="absolute inset-0">
                        <img
                             :src="currentThumbs.mobile"
                             :srcset="`${currentThumbs.mobile} 320w, ${currentThumbs.mobile2x} 640w, ${currentThumbs.large} 800w`"
                             sizes="(max-width: 480px) 150px, (max-width: 768px) 300px, 800px"
                             :alt="currentThumbs.alt"
                             fetchpriority="high"
                             loading="eager"
                             decoding="async"
                             width="320"
                             height="240"
                             class="w-full h-full object-contain cursor-pointer"
                             @click="openModal(currentIndex)">
                    </div>
                {% endwith %}
            </div>
            
            <!-- Navigation Arrows -->
            <button @click="previousImage()" 
                    x-show="imageData.length > 1"
                    aria-label="{% trans 'Previous image' %}"
                    class="absolute left-2 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-opacity"
                    @click.stop>
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <button @click="nextImage()" 
                    x-show="imageData.length > 1"
                    aria-label="{% trans 'Next image' %}"
                    class="absolute right-2 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-opacity"
                    @click.stop>
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        
        <!-- Thumbnail Navigation - All thumbnails visible -->
        <div class="flex space-x-2 overflow-x-auto p-1.5">
            {% for image in all_images %}
                {% oscar_thumbnail image.original "65x55" crop="center" as thumb_small %}
                <button @click="setCurrentImage({{ forloop.counter0 }})"
                        aria-label="{% trans 'View image' %} {{ forloop.counter }}"
                        class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden hover:ring-2 hover:ring-gray-300 md:transition-all md:duration-200"
                        :class="{ 'ring-2 ring-blue-500': currentIndex === {{ forloop.counter0 }} }">
                    <img src="{{ thumb_small.url }}"
                         alt="{{ product.get_title }} - {% trans 'Image' %} {{ forloop.counter }}"
                         loading="{% if forloop.counter <= 6 %}eager{% else %}lazy{% endif %}"
                         class="w-full h-full object-cover">
                </button>
            {% endfor %}
        </div>
        
    {% else %}
        <!-- Single Image Display -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden">
            <div class="aspect-w-4 aspect-h-3">
                {% with image=product.primary_image %}
                    {% oscar_thumbnail image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}
                    {% oscar_thumbnail image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}
                    {% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                    <img src="{{ thumb_mobile.url }}"
                         srcset="{{ thumb_mobile.url }} 320w, {{ thumb_mobile_2x.url }} 640w, {{ thumb_large.url }} 800w"
                         sizes="(max-width: 480px) 150px, (max-width: 768px) 300px, 800px"
                         alt='{{ product.get_title }}'
                         fetchpriority="high"
                         loading="eager"
                         decoding="async"
                         width="320"
                         height="240"
                         class="w-full h-full object-contain cursor-pointer"
                         @click="openModal(0)">
                {% endwith %}
            </div>
        </div>
    {% endif %}
    
    <!-- Modal for Full Size Images -->
    <div x-show="showModal" 
         x-cloak
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 md:backdrop-blur-sm"
         @click="closeModal()"
         @keydown.window="handleKeydown($event)">
        
        <!-- Modal Navigation Buttons - positioned relative to viewport -->
        <button @click="previousModalImage()" 
                x-show="imageData.length > 1"
                aria-label="{% trans 'Previous image' %}"
                class="fixed left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg z-10"
                @click.stop>
            <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
        </button>
        <button @click="nextModalImage()" 
                x-show="imageData.length > 1"
                aria-label="{% trans 'Next image' %}"
                class="fixed right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg z-10"
                @click.stop>
            <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
        </button>
        
        <!-- Close Button - positioned relative to viewport -->
        <button @click="closeModal()" 
                aria-label="{% trans 'Close image gallery' %}"
                class="fixed top-4 right-4 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg z-10"
                @click.stop>
            <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </button>
        
        <!-- Image Container -->
        <div class="relative max-w-4xl max-h-full min-h-96 p-4 flex items-center justify-center" @click.stop>
            <!-- Loading indicator -->
            <div x-show="isLoadingOriginal" class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
            
            <!-- Original image -->
            <img x-show="!isLoadingOriginal && currentOriginalSrc" 
                 :src="currentOriginalSrc" 
                 :alt="imageData[modalIndex]?.alt"
                 class="max-w-full max-h-full object-contain">
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('productGallery', () => ({
        currentIndex: 0, // Track current main image
        modalIndex: 0,
        showModal: false,
        isLoadingOriginal: false,
        currentOriginalSrc: null,
        loadedImages: new Set(), // Track which gallery images are loaded
        currentThumbs: {
            mobile: '',
            mobile2x: '',
            large: '',
            alt: ''
        },
        // Store image data - thumbnails generated on server, large versions loaded on demand
        imageData: [
            {% for image in all_images %}
            {
                originalUrl: '{{ image.original.url }}',
                alt: '{{ product.get_title }} - {% trans "Image" %} {{ forloop.counter }}',
                imageId: {{ image.id }},
                {% if forloop.first %}
                thumbnails: {
                    mobile: '{% oscar_thumbnail image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}{{ thumb_mobile.url }}',
                    mobile2x: '{% oscar_thumbnail image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}{{ thumb_mobile_2x.url }}',
                    large: '{% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}{{ thumb_large.url }}'
                }
                {% else %}
                thumbnails: null
                {% endif %}
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        
        init() {
            this.currentIndex = 0;
            this.loadedImages.add(0);
            // Set initial thumbs
            const first = this.imageData[0];
            this.currentThumbs = {
                mobile: first.thumbnails.mobile,
                mobile2x: first.thumbnails.mobile2x,
                large: first.thumbnails.large,
                alt: first.alt
            };
        },
        
        async setCurrentImage(index) {
            if (this.currentIndex === index) return;
            this.currentIndex = index;
            await this.loadGalleryImage(index);
        },
        
        async nextImage() {
            const newIndex = (this.currentIndex + 1) % this.imageData.length;
            await this.setCurrentImage(newIndex);
        },
        
        async previousImage() {
            const newIndex = this.currentIndex === 0 ? this.imageData.length - 1 : this.currentIndex - 1;
            await this.setCurrentImage(newIndex);
        },
        
        async loadGalleryImage(index) {
            if (this.loadedImages.has(index) && this.imageData[index].thumbnails) {
                this.currentThumbs = {
                    mobile: this.imageData[index].thumbnails.mobile,
                    mobile2x: this.imageData[index].thumbnails.mobile2x,
                    large: this.imageData[index].thumbnails.large,
                    alt: this.imageData[index].alt
                };
                return;
            }
            const imageData = this.imageData[index];
            if (!imageData.thumbnails) {
                // For now, use original for all sizes
                imageData.thumbnails = {
                    mobile: imageData.originalUrl,
                    mobile2x: imageData.originalUrl,
                    large: imageData.originalUrl
                };
            }
            this.currentThumbs = {
                mobile: imageData.thumbnails.mobile,
                mobile2x: imageData.thumbnails.mobile2x,
                large: imageData.thumbnails.large,
                alt: imageData.alt
            };
            this.loadedImages.add(index);
        },
        
        // Modal functionality
        async openModal(index) {
            this.modalIndex = index;
            this.showModal = true;
            document.body.style.overflow = 'hidden';
            await this.loadOriginalImage(index);
        },
        
        closeModal() {
            this.showModal = false;
            this.currentOriginalSrc = null;
            document.body.style.overflow = '';
        },
        
        async nextModalImage() {
            const newIndex = (this.modalIndex + 1) % this.imageData.length;
            this.modalIndex = newIndex;
            await this.loadOriginalImage(newIndex);
        },
        
        async previousModalImage() {
            const newIndex = this.modalIndex === 0 ? this.imageData.length - 1 : this.modalIndex - 1;
            this.modalIndex = newIndex;
            await this.loadOriginalImage(newIndex);
        },
        
        async loadOriginalImage(index) {
            const imageUrl = this.imageData[index].originalUrl;
            this.isLoadingOriginal = true;
            this.currentOriginalSrc = null;
            try {
                const img = new Image();
                img.onload = () => {
                    this.currentOriginalSrc = imageUrl;
                    this.isLoadingOriginal = false;
                };
                img.onerror = () => {
                    console.error('Failed to load original image:', imageUrl);
                    this.currentOriginalSrc = imageUrl;
                    this.isLoadingOriginal = false;
                };
                img.src = imageUrl;
            } catch (error) {
                console.error('Error loading original image:', error);
                this.currentOriginalSrc = imageUrl;
                this.isLoadingOriginal = false;
            }
        },
        
        handleKeydown(event) {
            if (this.showModal) {
                if (event.key === 'Escape') {
                    this.closeModal();
                } else if (event.key === 'ArrowRight' || event.key === 'd') {
                    this.nextModalImage();
                } else if (event.key === 'ArrowLeft' || event.key === 'a') {
                    this.previousModalImage();
                } else if (event.key === 'ArrowUp' || event.key === 'w') {
                    this.previousModalImage();
                } else if (event.key === 'ArrowDown' || event.key === 's') {
                    this.nextModalImage();
                } else if (event.key === ' ') {
                    event.preventDefault();
                    this.nextModalImage();
                }
            }
        }
    }));
});
</script>

{% endwith %}
