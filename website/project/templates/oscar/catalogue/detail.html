{% extends "oscar/layout_storefront.html" %}


{% load history_tags %}
{% load custom_currency_filters %}
{% load reviews_tags %}
{% load static %}
{% load custom_product_tags %}
{% load display_tags %}
{% load i18n %}
{% load cache %}
{% load custom_purchase_info_tags %}
{% load contact_info_tags %}
{% load tz %}
{% load catalogue_tags %}

{% block title %}
    {{ main_category }} {{ product.original_code_cleaned }} {{ main_manufacturer }} {{ main_model_cleaned }} {{ main_year }}
{% endblock %}

{% block description %}
    {{ product.get_meta_description|default:""|striptags }}
{% endblock %}

{% block keywords %}
    {{ product.get_meta_keywords|default:""|striptags }}
{% endblock %}

{% block open_graph %}
<meta property="og:title" content="{{ main_category }} {{ product.original_code_cleaned }} {{ main_manufacturer }} {{ main_model_cleaned }} {{ main_year }}" >
<meta property="og:description" content="{{ product.get_meta_description|default:""|striptags }}" >
<meta property="og:site_name" content="{{ site.domain }}" >
<meta property="og:image" content="{{ product.get_absolute_main_image_url }}" >
<meta property="product:price:amount" content="{{ purchase_info.price.incl_tax|unlocalize }}" >
<meta property="product:price:currency" content="EUR" >
<meta property="product:availability" content="{% if purchase_info.availability.is_available_to_buy %}in stock{% else %}out of stock{% endif %}" >
<meta property="og:product:condition" content="{{ product.get_condition_display }}" >
<meta property="og:url" content="{{ current_site }}" >
<meta property="og:type" content="product" >
{% endblock %}

{% block extrahead %}
    <!-- CRITICAL: Mobile-first preload strategy with DPR support -->
    {% with primary_image=product.primary_image %}
        {% if primary_image %}
            {% load image_tags %}
            {% oscar_thumbnail primary_image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}
            {% oscar_thumbnail primary_image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}
            {% oscar_thumbnail primary_image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
            
            <!-- Mobile-first: preload appropriate size for mobile devices -->
            <link rel="preload" as="image" href="{{ thumb_mobile_2x.url }}" fetchpriority="high" media="(max-width: 768px)">
            <!-- Desktop version for larger screens -->
            <link rel="preload" as="image" href="{{ thumb_large.url }}" fetchpriority="low" media="(min-width: 769px)">
        {% endif %}
    {% endwith %}

    <script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Product",
        "name": "{{ main_category }} {{ product.original_code_cleaned }} {{ main_manufacturer }} {{ main_model_cleaned }} {{ main_year }}",
        "brand": {"@type": "Brand", "name": "{{ main_manufacturer }}"},
        "mpn": "{{ product.original_code }}",
        "category": "{{ main_category }}",
        {% if product.color %}"color": "{{ product.color }}",{% endif %}
        {% if product.weight %}"weight": {"value": "{{ product.weight }}", "unitText": "kg"},{% endif %}
        {% if product.height %}"height": "{{ product.height }} cm",{% endif %}
        {% if product.width %}"width": "{{ product.width }} cm",{% endif %}
        {% if product.length %}"depth": "{{ product.length  }} cm",{% endif %}
        "image": [
            {% for image in product_images %}
                "https://{{ request.get_host }}/media/{{ image.original.name|escapejs }}"{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        "description": "{{ product.get_meta_description|default:"" }}",
        "review": {
            "@type": "Review",
            "reviewBody": "{% if product.condition == 'used' %}In good, working condition. This is a used part that has been tested and verified to be functioning properly.{% elif product.condition == 'new' %}Brand new part in original condition.{% elif product.condition == 'broken' %}Part is damaged or not functioning, suitable for repairs or parts.{% endif %}",
            "author": {
                "@type": "Person",
                "name": "Quality Control Specialist"
            },
            "reviewRating": {
                "@type": "Rating",
                "ratingValue": "{% if product.condition == 'new' %}5{% elif product.condition == 'used' %}4{% else %}2{% endif %}",
                "bestRating": "5",
                "worstRating": "1"
            }
        },
        "offers": {
            "@type": "Offer",
            "url": "https://{{ request.get_host }}{{ product.get_absolute_url }}",
            "priceCurrency": "EUR",
            "price": "{{ purchase_info.price.incl_tax|unlocalize }}",
            "priceValidUntil": "{% now 'Y' as current_year %}{{ current_year|add:'1' }}-{% now 'm-d' %}",
            {% if product.condition == 'used' %}
            "itemCondition": "http://schema.org/UsedCondition",
            {% elif product.condition == 'new' %}
            "itemCondition": "http://schema.org/NewCondition",
            {% elif product.condition == 'broken' %}
            "itemCondition": "http://schema.org/DamagedCondition",
            {% endif %}
            "availability": "{% if purchase_info.availability.is_available_to_buy %}http://schema.org/InStock{% else %}http://schema.org/OutOfStock{% endif %}",
            "hasMerchantReturnPolicy": {
                "@type": "MerchantReturnPolicy",
                {% if request.LANGUAGE_CODE == 'de' and current_branch == 'de' %}
                "returnPolicyCountry": "DE",
                "applicableCountry": "DE",
                {% elif request.LANGUAGE_CODE == 'pl' and current_branch == 'pl' %}
                "returnPolicyCountry": "PL",
                "applicableCountry": "PL",
                {% elif request.LANGUAGE_CODE == 'lt' and current_branch == 'lt' %}
                "returnPolicyCountry": "LT",
                "applicableCountry": "LT",
                {% elif request.LANGUAGE_CODE == 'fr' and current_branch == 'fr' %}
                "returnPolicyCountry": "FR",
                "applicableCountry": "FR",
                {% elif request.LANGUAGE_CODE == 'es' and current_branch == 'es' %}
                "returnPolicyCountry": "ES",
                "applicableCountry": "ES",
                {% endif %}
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "merchantReturnDays": 14,
                "returnMethod": "https://schema.org/ReturnByMail",
                "returnFees": "https://schema.org/ReturnFeesCustomerResponsibility"
            }
        }
    }
    </script>
    {{ block.super }}
{% endblock extrahead %}

{% block header %}{% endblock %}

{% block breadcrumbs %}
<nav aria-label="breadcrumb" class="mb-6">
    <div class="max-h-12 overflow-hidden">
        <ol class="flex flex-wrap items-center gap-x-2 gap-y-1 text-sm text-gray-600 leading-6">
            {% for c in category_ancestors %}
                {% if forloop.counter0 >= 1 %}
                <li class="flex items-center flex-shrink-0">
                    {% if forloop.counter0 > 1 %}
                    <svg class="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    {% endif %}
                    <a href="{{ c.get_absolute_url }}" class="hover:text-blue-800 transition-colors">{{ c.get_name|truncatewords:3 }}</a>
                </li>
                {% endif %}
            {% endfor %}
            <li class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-900 font-medium">
                    <span class="block lg:hidden truncate">{{ product.get_title|truncatewords:4 }}</span>
                    <span class="hidden lg:block">{{ product.get_title }}</span>
                </span>
            </li>
        </ol>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="grid grid-cols-1 lg:grid-cols-5 gap-8 mb-8">
    {% block product_gallery %}
    <div class="space-y-6 lg:col-span-3">
        <!-- Modern Image Gallery -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {% comment %} {% cache 500 product_galery LANGUAGE_CODE product.id request.branch %} {% endcomment %}
                {% include "oscar/catalogue/partials/gallery_storefront.html" %}
            {% comment %} {% endcache %} {% endcomment %}
        </div>

        <!-- Product Notice -->
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div class="flex items-start">
                <svg class="w-5 h-5 text-amber-800 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <p class="text-sm text-amber-800">
                    {% trans 'Before purchasing, please verify that this part fits your vehicle by checking the part number in the description and images. The buyer is responsible for ensuring compatibility.' %}
                </p>
            </div>
        </div>
    </div>
    {% endblock product_gallery %}

    {% block product_main %}
    <div class="space-y-6 lg:col-span-2">
    <!-- Product Title -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h1 class="text-lg lg:text-2xl font-bold text-gray-900 mb-4">
                {{ product.get_title }}
            </h1>

            {% block product_stock_record_and_basket_form %}
            {% get_saved_products as saved_products %}
            {% if purchase_info.availability.is_available_to_buy %}
            <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="space-y-6" x-data="{ quantity: 1 }">
                {% csrf_token %}
                <input id="id_product_id" name="product_id" value="{{ product.id }}" type="hidden">

                <!-- New Layout: Price and Quantity in one row, Buy now in second row -->
                <div class="space-y-6">
                    <!-- First row: Price and Quantity -->
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Price and Offer -->
                        <div class="space-y-2">
                            <div class="text-center">
                                <div class="text-2xl lg:text-3xl font-normal text-gray-900">
                                    {% include "oscar/catalogue/partials/stock_record.html" with verbose=1 user_currency=user_currency %}
                                </div>
                                {% if discount_info.has_discount %}
                                <div class="text-sm lg:text-lg text-gray-500 line-through">
                                    {{ discount_info.old_price_incl_tax|user_currency:user_currency }}
                                </div>
                                {% endif %}
                            </div>
                            {% if product.active %}
                            <div class="text-center">
                                <span class="inline-block text-blue-600 hover:text-blue-800 transition-colors text-xs lg:text-sm font-medium cursor-pointer"
                                      data-action="price-offer"
                                      data-product-id="{{ product.pk }}">
                                    {% trans 'Offer your price' %}
                                </span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Quantity and Stock -->
                        <div class="space-y-2">
                            <div class="flex flex-col items-center space-y-2">
                                <div class="flex items-center border border-gray-300 rounded-lg overflow-hidden bg-white">
                                    <button type="button" 
                                            @click="quantity = Math.max(1, quantity - 1)" 
                                            class="w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center text-gray-600 hover:text-gray-800 hover:bg-gray-50 transition-colors duration-200 text-sm lg:text-lg font-medium">
                                        −
                                    </button>
                                    <input type="number" 
                                           name="quantity" 
                                           id="id_quantity" 
                                           x-model="quantity" 
                                           min="1" 
                                           max="{{ purchase_info.stockrecord.num_in_stock }}"
                                           class="w-12 h-8 lg:w-16 lg:h-10 text-center border-0 focus:ring-0 focus:outline-none text-gray-900 font-medium text-sm lg:text-base [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none" 
                                           aria-label="{% trans 'Quantity' %}">
                                    <button type="button"
                                            @click="quantity = Math.min({{ purchase_info.stockrecord.num_in_stock }}, quantity + 1)"
                                            :disabled="quantity >= {{ purchase_info.stockrecord.num_in_stock }}"
                                            :class="{ 'text-gray-400 cursor-not-allowed': quantity >= {{ purchase_info.stockrecord.num_in_stock }}, 'text-gray-600 hover:text-gray-800 hover:bg-gray-50': quantity < {{ purchase_info.stockrecord.num_in_stock }} }"
                                            class="w-8 h-8 lg:w-10 lg:h-10 flex items-center justify-center transition-colors duration-200 text-sm lg:text-lg font-medium">
                                        +
                                    </button>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-xs lg:text-sm text-gray-600">
                                    {% trans 'Available stock' %}: <span class="font-medium text-green-700">{{ purchase_info.stockrecord.num_in_stock }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second row: Buy now button (full width) -->
                    {% if not product.is_full_car %}
                    <div class="flex items-center space-x-3">
                        {% if user.is_authenticated %}
                        <div
                            x-data="{
                                saved: {% if product.id|is_saved_product:saved_products %}true{% else %}false{% endif %},
                                saving: false,
                                error: false,
                                errorMessage: '',
                                saveForLater() {
                                    this.saving = true;
                                    this.error = false;

                                    const formData = new FormData();
                                    formData.append('product_id', '{{ product.id }}');
                                    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

                                    fetch('{% url 'basket:save-for-later' %}', {
                                        method: 'POST',
                                        body: formData,
                                        headers: {
                                            'X-Requested-With': 'XMLHttpRequest',
                                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                        }
                                    })
                                    .then(response => {
                                        if (!response.ok) {
                                            return response.json().then(data => {
                                                throw new Error(data.message || 'Network response was not ok');
                                            });
                                        }
                                        return response.json();
                                    })
                                    .then(data => {
                                        this.saving = false;
                                        if (data.success) {
                                            this.saved = true;
                                            if (!data.already_saved) {
                                                setTimeout(() => {
                                                    // Keep the heart filled, just remove any notification
                                                }, 2000);
                                            }
                                        } else {
                                            this.error = true;
                                            this.errorMessage = 'Failed to save product';
                                            setTimeout(() => {
                                                this.error = false;
                                            }, 3000);
                                        }
                                    })
                                    .catch(error => {
                                        this.saving = false;
                                        this.error = true;
                                        this.errorMessage = error.message || 'An error occurred';
                                        console.error('Error:', error);
                                        setTimeout(() => {
                                            this.error = false;
                                        }, 3000);
                                    });
                                }
                            }"
                            class="flex-shrink-0"
                        >
                            <button type="button"
                                    @click="!saved && saveForLater()"
                                    class="p-2 focus:outline-none"
                                    x-bind:disabled="saving || saved"
                                    x-bind:title="error ? errorMessage : (saved ? '{% trans "Saved to your list" %}' : '{% trans "Save for later" %}')"
                            >
                                <!-- Empty bookmark (when not saved) -->
                                <svg x-show="!saved && !saving" class="w-6 h-6 text-blue-900" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
                                </svg>

                                <!-- Filled bookmark (when saved) -->
                                <svg x-show="saved" x-cloak class="w-6 h-6 text-blue-900" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z" clip-rule="evenodd" />
                                </svg>

                                <!-- Spinner (when saving) -->
                                <svg x-show="saving" x-cloak class="w-6 h-6 text-blue-900 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </button>
                        </div>
                        {% endif %}

                        {% if product.active %}
                        <button type="submit" class="flex-1 btn-primary py-3 text-lg font-normal">
                            {% trans 'Buy now' %}
                        </button>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="w-full">
                        <span class="inline-block w-full px-4 py-2 bg-blue-600 text-white text-sm font-normal rounded shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out text-center cursor-pointer"
                              data-action="contact-vendor"
                              data-product-id="{{ product.id }}">{% trans 'Contact vendor' %}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Fast Shipping Notice -->
                {% if time_left_for_today_shipping %}
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <div class="flex items-center justify-center mb-2">
                        <svg class="w-5 h-5 text-green-800 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium text-green-800">{% trans 'Fast shipping' %}</span>
                    </div>
                    <p class="text-xs text-green-800">
                        {% trans 'Place order in coming' %} <span class="font-bold js-countDown" data-start="{{ time_left_for_today_shipping }}">01:08:45</span> {% trans 'and we will send out your order today!' %}
                    </p>
                </div>
                {% endif %}
            </form>
            {% endif %}
            {% endblock %}
        </div>

        <!-- Shipping Methods -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-normal text-gray-900 mb-4">{% trans 'Available shipping methods for:' %}</h2>
            {% include 'oscar/basket/partials/shipping_storefront.html' with product=product %}
        </div>
    </div>
    {% endblock product_main %}
</div>

<!-- Product Details and Contact Information Tabs (Full Width) -->
<div class="mb-8" x-data="{ activeTab: 'details' }">
    <!-- Tab Buttons -->
    <div class="mb-6 flex justify-center border-b border-gray-200">
        <button
            @click="activeTab = 'details'"
            :class="{ 'border-blue-800 text-blue-800': activeTab === 'details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'details' }"
            class="py-4 px-6 block font-normal leading-5 border-b-2 focus:outline-none transition-colors duration-150 ease-in-out">
            {% trans 'Product Details' %}
        </button>
        
        {% comment %} Check if eBay advertisement exists for this product {% endcomment %}
        {% if product.pap_product_id and ebay_ad %}
            <button
                @click="activeTab = 'purchase'"
                :class="{ 'border-blue-800 text-blue-800': activeTab === 'purchase', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'purchase' }"
                class="py-4 px-6 block font-normal leading-5 border-b-2 focus:outline-none transition-colors duration-150 ease-in-out">
                {% trans 'Other Purchase Options' %}
            </button>
        {% endif %}
        
        <button
            @click="activeTab = 'contact'"
            :class="{ 'border-blue-800 text-blue-800': activeTab === 'contact', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'contact' }"
            class="py-4 px-6 block font-normal leading-5 border-b-2 focus:outline-none transition-colors duration-150 ease-in-out">
            {% trans 'Contact Information' %}
        </button>
    </div>

    <!-- Product Details Tab Content -->
    <section x-show="activeTab === 'details'" x-cloak class="mb-12">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Product Information -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">{% trans 'Product Information' %}</h2>
                    <dl class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Name' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_category|safe }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Commercial ID" %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ product.commercial_id }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Product code (MPN)" %}</dt>
                            <dd class="text-sm text-gray-900 text-right" title="{{ product.original_code }}">{{ product.original_code|truncatewords:4|default:"--" }}</dd>
                        </div>
                        {% if purchase_info.price.exists and purchase_info.price.is_tax_known %}
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Price (incl. tax)" %}</dt>
                            <dd class="text-sm font-semibold text-gray-900 text-right">{{ purchase_info.price.incl_tax|user_currency:user_currency }}</dd>
                        </div>
                        {% endif %}
                        <div class="flex justify-between py-2 border-b border-gray-100" x-data="{ showCondition: false }">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Condition" %}</dt>
                            <dd class="text-sm text-right">
                                <button @click="showCondition = !showCondition" class="text-amber-800 hover:text-blue-800 transition-colors">
                                    {{ product.get_condition_display }}
                                </button>
                                <div x-show="showCondition" class="mt-2 p-3 bg-amber-50 rounded-md text-xs text-amber-800">
                                    {% if product.condition == 'new' %}{{ product.explain_condition_new }}{% else %}{{ product.explain_condition_used }}{% endif %}
                                </div>
                            </dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Drive position' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ product.get_drive_position_display|default:"--" }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Attributes' %}</dt>
                            <dd class="text-sm text-gray-900 text-right" title="{{ product.get_extra_attributes_line }}">{{ product.get_extra_attributes_line|truncatewords:10|default:"--" }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Color" %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ product.color|default:"--" }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans "Color code" %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ product.color_code|default:"--" }}</dd>
                        </div>
                    </dl>
                </div>

                <!-- Vehicle Information -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">{% trans 'Vehicle Information' %}</h2>
                    <dl class="space-y-3">
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Manufacturer' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_tecpap_attr.manufacturer|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Model' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_model.name|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Year' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_year|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Type' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_type.name|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'kW' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_type.kw_from|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'hp' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_type.hp_from|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'cc' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_type.ccm|default:'--' }}</dd>
                        </div>
                        <div class="flex justify-between py-2 border-b border-gray-100">
                            <dt class="text-sm font-medium text-gray-600">{% trans 'Fuel' %}</dt>
                            <dd class="text-sm text-gray-900 text-right">{{ main_type.fuel|default:'--' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </section>

    <!-- Purchase Options Tab Content -->
    {% if product.pap_product_id and ebay_ad %}
        <section x-show="activeTab === 'purchase'" x-cloak class="mb-12">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="text-center space-y-6">
                    <p class="text-normal text-gray-600 mb-8">
                        {% trans "For added flexibility, you can also buy this part through eBay, taking advantage of eBay's payment protection for extra confidence in your transaction." %}
                    </p>
                    
                    <div class="flex justify-center">
                        {% if is_live_deployment %}
                            <a href="https://www.ebay.com/itm/{{ ebay_ad.ebay_listing_id }}" 
                               target="_blank" 
                               class="inline-flex items-start hover:opacity-80 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded p-2"
                               title="{% trans 'View on eBay' %}">
                                <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="120" height="48" data-name="eBay logo" viewBox="0 0 1000 400.8">
                                    <path d="M633 212.5c-45.4 1.5-73.6 9.7-73.6 39.7 0 19.3 15.5 40.3 54.7 40.3 52.5 0 80.6-28.6 80.6-75.6v-5.2c-18.4 0-41.1.2-61.6.8zm111.8 62.1c0 14.6.5 29 1.7 42H700a273.1 273.1 0 0 1-1.7-31.6C673 316 643 325 601.5 325c-61.7 0-94.7-32.6-94.7-70.3 0-54.6 44.9-73.9 122.8-75.7 21.4-.5 45.3-.5 65.1-.5V173c0-36.5-23.4-51.6-64-51.6-30.2 0-52.4 12.5-54.7 34h-52.7c5.6-53.7 62-67.3 111.8-67.3 59.5 0 109.7 21.2 109.7 84.1z" style="fill:#ffbc13;fill-opacity:1;stroke-width:0"/>
                                    <path d="M199.6 185.9c-2-47-35.7-64.5-72-64.5-38.9 0-70 19.8-75.5 64.5zM51 219.2c2.7 45.5 34.1 72.4 77.2 72.4 30 0 56.5-12.2 65.4-38.7h51.7c-10 53.8-67.2 72-116.3 72-89.4 0-129-49.2-129-115.6 0-73 41-121.2 129.8-121.2 70.7 0 122.5 37 122.5 117.8v13.3z" style="fill:#f12c2d;fill-opacity:1;stroke-width:0"/>
                                    <path d="M380.8 290.6c46.6 0 78.5-33.5 78.5-84 0-50.7-31.9-84.2-78.5-84.2-46.3 0-78.4 33.5-78.4 84.1 0 50.6 32.1 84.1 78.4 84.1zM252.3 0h50v125.9C327 96.6 360.9 88 394.2 88c55.8 0 117.8 37.7 117.8 119 0 68.2-49.3 117.8-118.8 117.8-36.3 0-70.5-13-91.6-38.9 0 10.3-.6 20.7-1.7 30.6h-49.2c.8-16 1.7-35.7 1.7-51.8z" style="fill:#0968f6;fill-opacity:1;stroke-width:0"/>
                                    <path d="M1000 96.5 845 400.8h-56l44.5-84.5L716.9 96.5h58.6l85.8 171.7L947 96.5z" style="fill:#93c822;fill-opacity:1;stroke-width:0"/>
                                </svg>
                                <svg class="w-4 h-4 ml-2 text-gray-500 self-start" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </a>
                        {% else %}
                            <a href="https://sandbox.ebay.com/itm/{{ ebay_ad.ebay_listing_id }}" 
                               target="_blank" 
                               class="inline-flex items-start hover:opacity-80 transition-opacity duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded p-2"
                               title="{% trans 'View on eBay (Sandbox)' %}">
                                <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="120" height="48" data-name="eBay logo" viewBox="0 0 1000 400.8">
                                    <path d="M633 212.5c-45.4 1.5-73.6 9.7-73.6 39.7 0 19.3 15.5 40.3 54.7 40.3 52.5 0 80.6-28.6 80.6-75.6v-5.2c-18.4 0-41.1.2-61.6.8zm111.8 62.1c0 14.6.5 29 1.7 42H700a273.1 273.1 0 0 1-1.7-31.6C673 316 643 325 601.5 325c-61.7 0-94.7-32.6-94.7-70.3 0-54.6 44.9-73.9 122.8-75.7 21.4-.5 45.3-.5 65.1-.5V173c0-36.5-23.4-51.6-64-51.6-30.2 0-52.4 12.5-54.7 34h-52.7c5.6-53.7 62-67.3 111.8-67.3 59.5 0 109.7 21.2 109.7 84.1z" style="fill:#ffbc13;fill-opacity:1;stroke-width:0"/>
                                    <path d="M199.6 185.9c-2-47-35.7-64.5-72-64.5-38.9 0-70 19.8-75.5 64.5zM51 219.2c2.7 45.5 34.1 72.4 77.2 72.4 30 0 56.5-12.2 65.4-38.7h51.7c-10 53.8-67.2 72-116.3 72-89.4 0-129-49.2-129-115.6 0-73 41-121.2 129.8-121.2 70.7 0 122.5 37 122.5 117.8v13.3z" style="fill:#f12c2d;fill-opacity:1;stroke-width:0"/>
                                    <path d="M380.8 290.6c46.6 0 78.5-33.5 78.5-84 0-50.7-31.9-84.2-78.5-84.2-46.3 0-78.4 33.5-78.4 84.1 0 50.6 32.1 84.1 78.4 84.1zM252.3 0h50v125.9C327 96.6 360.9 88 394.2 88c55.8 0 117.8 37.7 117.8 119 0 68.2-49.3 117.8-118.8 117.8-36.3 0-70.5-13-91.6-38.9 0 10.3-.6 20.7-1.7 30.6h-49.2c.8-16 1.7-35.7 1.7-51.8z" style="fill:#0968f6;fill-opacity:1;stroke-width:0"/>
                                    <path d="M1000 96.5 845 400.8h-56l44.5-84.5L716.9 96.5h58.6l85.8 171.7L947 96.5z" style="fill:#93c822;fill-opacity:1;stroke-width:0"/>
                                </svg>
                                <svg class="w-4 h-4 ml-2 text-gray-500 self-start" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </section>
    {% endif %}

    <!-- Contact Information Tab Content -->
    <section x-show="activeTab === 'contact'" x-cloak class="mb-12">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {% with product.owner.account as vendor %}
                <!-- Contact Details -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">{% trans 'Contact Details' %}</h2>
                    <div class="space-y-3">
                        {% if current_branch == 'eu' %}
                            {% if product.subowner %}
                                {% with product.subowner.account as manager %}
                                    {% if manager.contact_name %}
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ manager.contact_name }}</span>
                                    </div>
                                    {% endif %}
                                    {% comment %} {% if vendor.contact_address %}
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ vendor.contact_address }}</span>
                                    </div>
                                    {% endif %} {% endcomment %}
                                    {% if manager.contact_phone %}
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ manager.contact_phone }}</span>
                                    </div>
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                {% if vendor.contact_name %}
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ vendor.contact_name }}</span>
                                </div>
                                {% endif %}
                                {% comment %} {% if vendor.contact_address %}
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ vendor.contact_address }}</span>
                                </div>
                                {% endif %} {% endcomment %}
                                {% if vendor.contact_phone %}
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ vendor.contact_phone }}</span>
                                </div>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ request.branch.contact_name }}</span>
                            </div>
                            {% comment %} <div class="flex items-start">
                                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ request.branch.contact_address }}</span>
                            </div> {% endcomment %}
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ request.branch.contact_phone }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Additional Contact Info and Actions -->
                <div class="space-y-4">
                    {% comment %} <div class="space-y-3">
                        {% if current_branch == 'eu' %}
                            {% if product.subowner %}
                                {% with product.subowner.account as manager %}
                                    {% if vendor.contact_skype %}
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">{{ vendor.contact_skype }}</span>
                                    </div>
                                    {% endif %}
                                    {% if vendor.get_contact_info %}
                                    <div class="text-sm text-gray-700">{{ vendor.get_contact_info|safe }}</div>
                                    {% endif %}
                                {% endwith %}
                            {% else %}
                                {% if vendor.contact_skype %}
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">{{ vendor.contact_skype }}</span>
                                </div>
                                {% endif %}
                                {% if vendor.get_contact_info %}
                                <div class="text-sm text-gray-700">{{ vendor.get_contact_info|safe }}</div>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            {% if request.branch.contact_skype %}
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm text-gray-700">{{ request.branch.contact_skype }}</span>
                            </div>
                            {% endif %}
                            {% if request.branch.get_contact_info %}
                            <div class="text-sm text-gray-700">{{ request.branch.get_contact_info|safe }}</div>
                            {% endif %}
                        {% endif %}
                    </div> {% endcomment %}

                    <!-- Contact Vendor Button -->
                    <div class="pt-4">
                        <span class="btn-primary w-full text-center cursor-pointer"
                              data-action="contact-vendor"
                              data-product-id="{{ product.id }}">
                            {% trans 'Send Message' %}
                        </span>
                    </div>
                </div>
                {% endwith %}
            </div>
        </div>
    </section>
</div>
{% endblock content %}

{% block extrascripts %}
<script>
    // Alpine.js component for product detail page
    document.addEventListener('alpine:init', () => {
        Alpine.data('productDetail', () => ({
            init() {
                // Initialize any product detail specific functionality
            }
        }));
    });

    // Handle robot-hidden links
    document.addEventListener('DOMContentLoaded', function() {
        // Handle price offer and contact vendor clicks
        document.addEventListener('click', function(e) {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.getAttribute('data-action');
            const productId = target.getAttribute('data-product-id');

            if (action === 'price-offer') {
                // Construct URL dynamically to avoid robots
                const currentLang = document.documentElement.lang || '{{ LANGUAGE_CODE }}';
                window.location.href = '/' + currentLang + '/ctg/price-offer/' + productId + ')/';
            } else if (action === 'contact-vendor') {
                // Construct URL dynamically to avoid robots
                const currentLang = document.documentElement.lang || '{{ LANGUAGE_CODE }}';
                window.location.href = '/' + currentLang + '/ctg/contact-vendor/' + productId + '/';
            }
        });
    });

    // DPD Cash Countries configuration
    var DPD_CASH_COUNTRIES = [{% for country in dpd_cash_countries %}'{{ country }}'{% if not forloop.last %},{% endif %}{% endfor %}];

    {% if debug %}
    // Enhanced LCP and Preload debugging in development
    document.addEventListener('DOMContentLoaded', function() {
        // Device and viewport information
        console.log('📱 Device Info:', {
            devicePixelRatio: window.devicePixelRatio,
            viewportWidth: window.innerWidth,
            screenWidth: screen.width,
            userAgent: navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'
        });

        // Check if preload links are working
        const preloadLinks = document.querySelectorAll('link[rel="preload"][as="image"]');
        console.log('🖼️ Image preload links found:', preloadLinks.length);
        preloadLinks.forEach((link, index) => {
            console.log(`Preload ${index + 1}:`, {
                href: link.href.split('/').pop(),
                fetchpriority: link.getAttribute('fetchpriority'),
                media: link.getAttribute('media') || 'all',
                matches: link.getAttribute('media') ? window.matchMedia(link.getAttribute('media')).matches : true
            });
        });

        // Check main image srcset
        const mainImage = document.querySelector('.product-gallery img[srcset]');
        if (mainImage) {
            console.log('🎯 Main image info:', {
                src: mainImage.src.split('/').pop(),
                srcset: mainImage.srcset,
                sizes: mainImage.sizes
            });
            
            // Parse srcset URLs
            const srcsetUrls = mainImage.srcset.split(',').map(item => {
                const parts = item.trim().split(' ');
                return {
                    url: parts[0].split('/').pop(),
                    width: parts[1]
                };
            });
            
            // Check preload URLs
            const preloadLinks = document.querySelectorAll('link[rel="preload"][as="image"]');
            const preloadUrls = Array.from(preloadLinks).map(link => ({
                url: link.href.split('/').pop(),
                media: link.getAttribute('media') || 'all',
                matches: link.getAttribute('media') ? window.matchMedia(link.getAttribute('media')).matches : true
            }));
            
            console.log('🔗 URL Comparison:', {
                srcsetUrls: srcsetUrls,
                preloadUrls: preloadUrls,
                urlsMatch: srcsetUrls.some(s => preloadUrls.some(p => p.url === s.url && p.matches))
            });
        }

        // LCP observer
        if ('PerformanceObserver' in window) {
            let lcpEntry = null;
            const po = new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                const lastEntry = entries[entries.length - 1];
                lcpEntry = lastEntry; // Store globally
                console.log('🚀 LCP detected:', {
                    element: lastEntry.element?.tagName,
                    url: lastEntry.url?.split('/').pop(),
                    loadTime: Math.round(lastEntry.loadTime),
                    imageUsed: lastEntry.url?.includes('640') ? '640px' : lastEntry.url?.includes('800') ? '800px' : '320px'
                });
            });
            po.observe({ entryTypes: ['largest-contentful-paint'] });
            
            // Performance summary after LCP
            setTimeout(() => {
                if (lcpEntry) {
                    console.log('📊 Final Performance Summary:', {
                        lcpTime: Math.round(lcpEntry.loadTime) + 'ms',
                        lcpGood: lcpEntry.loadTime < 2500 ? '✅ GOOD' : '❌ NEEDS WORK',
                        renderingOptimized: lcpEntry.renderTime - lcpEntry.loadTime < 100 ? '✅ FAST' : '⚠️ SLOW',
                        overallGrade: lcpEntry.loadTime < 1500 ? 'A+' : lcpEntry.loadTime < 2500 ? 'B+' : 'C',
                        nextSteps: lcpEntry.loadTime < 1500 ? 'Excellent! Consider image optimization' : 'Focus on render blocking resources'
                    });
                }
            }, 3000);
        }

        // CSS Loading detection
        const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
        console.log('📄 Stylesheets loading:', {
            count: stylesheets.length,
            styles: Array.from(stylesheets).map((link, index) => ({
                index: index,
                href: link.href.split('/').pop(),
                loaded: link.sheet !== null,
                media: link.media || 'all',
                loadTime: link.sheet ? 'loaded' : 'loading...'
            })),
            allLoaded: Array.from(stylesheets).every(link => link.sheet !== null)
        });

        // Server response timing
        if ('performance' in window && performance.timing) {
            const timing = performance.timing;
            const serverResponseTime = timing.responseEnd - timing.requestStart;
            const networkLatency = timing.responseStart - timing.requestStart;
            
            console.log('⏱️ Server Performance:', {
                serverResponseTime: Math.round(serverResponseTime) + 'ms',
                networkLatency: Math.round(networkLatency) + 'ms',
                breakdown: {
                    dns: Math.round(timing.domainLookupEnd - timing.domainLookupStart),
                    tcp: Math.round(timing.connectEnd - timing.connectStart), 
                    serverProcessing: Math.round(timing.responseStart - timing.requestStart),
                    contentTransfer: Math.round(timing.responseEnd - timing.responseStart),
                    domContentLoaded: Math.round(timing.domContentLoadedEventEnd - timing.navigationStart),
                    renderBlockingResources: Math.round(timing.domContentLoadedEventStart - timing.responseEnd)
                }
            });
        }

        // Check Paint Timing API for FCP
        if ('PerformanceObserver' in window) {
            const paintObserver = new PerformanceObserver((entryList) => {
                const entries = entryList.getEntries();
                entries.forEach(entry => {
                    console.log(`🎨 ${entry.name}: ${Math.round(entry.startTime)}ms`);
                });
            });
            paintObserver.observe({ entryTypes: ['paint'] });
        }
    });
    {% endif %}

    // Countdown timer functionality (if needed)
    document.addEventListener('DOMContentLoaded', function() {
        const countdownElement = document.querySelector('.js-countDown');
        if (countdownElement) {
            const startTime = parseInt(countdownElement.dataset.start);
            if (startTime > 0) {
                let timeLeft = startTime;
                const timer = setInterval(() => {
                    const hours = Math.floor(timeLeft / 3600);
                    const minutes = Math.floor((timeLeft % 3600) / 60);
                    const seconds = timeLeft % 60;

                    countdownElement.textContent =
                        String(hours).padStart(2, '0') + ':' +
                        String(minutes).padStart(2, '0') + ':' +
                        String(seconds).padStart(2, '0');

                    if (timeLeft <= 0) {
                        clearInterval(timer);
                    }
                    timeLeft--;
                }, 1000);
            }
        }
    });
</script>
{{ block.super }}
{% endblock %}

{% block reviews %}{% endblock %}


