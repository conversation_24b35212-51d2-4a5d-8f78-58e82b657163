{% load i18n %}
{% load widget_tweaks %}

<!-- Choices.js CSS & JS (lightweight, no dependencies) -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css" />
<script src="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js" defer></script>

<!-- Fix Choices.js styling -->
<style>
.choices__list--dropdown {
    border-radius: 0.5rem !important;
    border: 1px solid #d1d5db !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}
.choices__item--choice {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    padding: 0.5rem 1rem !important;
}
.choices__inner {
    border-radius: 0.5rem !important;
    border: 1px solid #d1d5db !important;
    min-height: 2.5rem !important;
}
.choices[data-type*="select-one"] .choices__inner {
    padding: 0.5rem 0.75rem !important;
}
</style>

<div class="space-y-4">
    <!-- Row 1: Country Selection -->
    <div class="space-y-2">
        <select id="shipping_country_select" 
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                data-product-id="{{ product.id }}"
                data-product-weight="{{ product.package_weight }}"
                placeholder="{% trans 'Choose your country' %}">
            <option value="">{% trans 'Choose your country' %}</option>
            {% for country in shipping_form.country.field.queryset %}
                <option value="{{ country.iso_3166_1_a2 }}" {% if request.country_code == country.iso_3166_1_a2 %}selected{% endif %}>{{ country.printable_name }}</option>
            {% endfor %}
        </select>
    </div>

    <!-- Row 2: Available Shipping Methods -->
    <div id="shipping_methods_display" class="space-y-2" style="display: none;">
        <hr class="border-gray-200 my-4">
        <div id="shipping_methods_list" class="space-y-3">
            <!-- Methods will be populated by JavaScript -->
        </div>
    </div>

    <!-- Cash on Delivery Notice -->
    <div id="cod_notice" class="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center" style="display: none;">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 122.88 118.34" 
             width="72" height="48" 
             alt="Cash on Delivery icon"
             class="mr-3 fill-green-800">
            <title>cash-on-delivery</title>
            <path d="M29.8 7.5a2.5 2.5 0 0 0-2.6 2.5v5.8l-7.4 7.4V7a7 7 0 0 1 2-4.9 7 7 0 0 1 4.9-2H116a7 7 0 0 1 4.9 2 7.1 7.1 0 0 1 2 4.9v73.3c0 1.9-.5 4-2 5a7 7 0 0 1-4.9 2H61.7A22 22 0 0 0 60 82l2.4-2.4h50.4a2.5 2.5 0 0 0 2.5-2.5V10a2.5 2.5 0 0 0-2.5-2.5H99v26.4l-14.6-7.3L70 33.9V7.5ZM9.3 77.7l-3.1 18a1.4 1.4 0 0 1-.3.6 7 7 0 0 0-1.7 3.3 1.8 1.8 0 0 0 .5 1.8L16.4 113a5.3 5.3 0 0 0 2.8 1.4 7 7 0 0 0 3.7.1h.1l2.2-.5c3.3-.8 6.2-1.5 9-4l3.4-3.6a1.1 1.1 0 0 1 .1-.2l.9-.8c2.3-2.3 5.3-5.2 3.5-7.7l-1.4-1.4-2 2-1.8 1.5a1.5 1.5 0 0 1-2-2c.4-.6 1.1-1.2 1.8-1.8 2.3-2 5-4.4 3.6-6.5l-1.4-1.3-.2-.3-2.5 2.3-1.7 1.6a1.5 1.5 0 0 1-2.1 0 1.4 1.4 0 0 1 0-2l1.9-1.8c2.3-2 5-4.4 3.6-6.5l-1.4-1.4a1.8 1.8 0 0 1-.2-.3l-4 4a1.5 1.5 0 0 1-2.1-2l7.5-7.6c1.8-1.8 2.3-3.7 1.8-5.1-.2-.5 0-.5-.4-1a3.3 3.3 0 0 0-.6-.4c-.1 0-.2 0-.3-.2a2.8 2.8 0 0 0-.4-.1c-1.3-.5-2.5.3-4.1 1.7a8 8 0 0 0-.6.5L17.3 85.3a1.5 1.5 0 0 1-2.1 0 1.4 1.4 0 0 1-.1-1.9l-5.8-5.7Zm9.5 1 .5-.4 10.3-10.4A10 10 0 1 1 41 66a2.4 2.4 0 0 1 .2.3 6.1 6.1 0 0 1 .8 1l12.8-12.8a5 5 0 0 1 0-7.1l-9.5-9.6a5 5 0 0 1-7.2 0L11.5 64.3a5 5 0 0 1 0 7.1l7.3 7.4Zm22.5-3.3a10.5 10.5 0 0 1-1.2 1.4l-1.5 1.5a1.7 1.7 0 0 1 .4.2l1.4 1.5a1.8 1.8 0 0 1 .2.2 5 5 0 0 1 .3 6 1.5 1.5 0 0 1 .5.4l1.4 1.4a1.8 1.8 0 0 1 .2.3 5 5 0 0 1 0 6.3 1.2 1.2 0 0 1 .2.2l1.4 1.4a2.8 2.8 0 0 1 .2.3c3.4 4.5-.6 8.4-3.8 11.5l-.8.8-3.6 3.8c-3.3 3-6.6 3.8-10.3 4.7l-2.1.6a12.7 12.7 0 0 1-5.6.3 8.1 8.1 0 0 1-4.3-2.2L1.9 103.5a4.6 4.6 0 0 1-1.3-4.4A11.8 11.8 0 0 1 3 94.4l3.3-18.8v-.8L0 68.4l42.2-42.2 24.1 24.2-25 25Z"/>
        </svg>
        <div class="text-sm text-green-800">
            <p>{% trans 'Cash on Delivery Available with Economy shipping' %}</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('shipping_country_select');
    const methodsDisplay = document.getElementById('shipping_methods_display');
    const methodsList = document.getElementById('shipping_methods_list');
    const codNotice = document.getElementById('cod_notice');
    
    // Initialize Choices.js when it's loaded
    function initChoices() {
        if (window.Choices) {
            const choices = new Choices(countrySelect, {
                searchEnabled: true,
                itemSelectText: '',
                noResultsText: '{% trans "No countries found" %}',
                placeholder: true,
                placeholderValue: '{% trans "Choose your country" %}'
            });
            
            // Trigger shipping methods if country already selected
            if (countrySelect.value) {
                setTimeout(() => {
                    countrySelect.dispatchEvent(new Event('change'));
                }, 100);
            }
        } else {
            // Retry in 100ms if Choices.js not loaded yet
            setTimeout(initChoices, 100);
        }
    }
    
    // Start trying to initialize Choices.js
    initChoices();
    
    // DPD Cash on Delivery countries
    const codCountries = [{% for country in dpd_cash_countries %}'{{ country }}'{% if not forloop.last %},{% endif %}{% endfor %}];
    
    if (countrySelect) {
        countrySelect.addEventListener('change', function() {
            const selectedCountry = this.value;
            const productId = this.dataset.productId;
            const productWeight = this.dataset.productWeight;
            
            if (!selectedCountry) {
                hideAllSections();
                return;
            }
            
            // Show loading state
            showLoadingState();
            
            // Get shipping methods for both economy and express
            Promise.all([
                getShippingPrice(productId, selectedCountry, 'fixed-price-economy'),
                getShippingPrice(productId, selectedCountry, 'fixed-price-express')
            ]).then(([economyResponse, expressResponse]) => {
                displayShippingMethods(economyResponse, expressResponse, selectedCountry, productWeight);
            }).catch(error => {
                console.error('Error fetching shipping prices:', error);
                hideAllSections();
            });
        });
    }
    
    // Simple JSON API call
    function getShippingPrice(productId, country, method) {
        const url = new URL('{% url "get_shipping_price" %}', window.location.origin);
        url.searchParams.set('product_id', productId);
        url.searchParams.set('country', country);
        url.searchParams.set('method', method);
        
        return fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                return data;
            });
    }
    
    function displayShippingMethods(economyResponse, expressResponse, country, weight) {
        const methods = [];
        
        // Add economy method if available
        if (economyResponse.available && economyResponse.price !== null) {
            methods.push({
                name: '{% trans "Economy shipping (3-5 work days)" %}',
                price: economyResponse.price,
                code: 'economy',
                delay: economyResponse.delay
            });
        }
        
        // Add express method if available  
        if (expressResponse.available && expressResponse.price !== null) {
            methods.push({
                name: '{% trans "Express shipping (1-3 work days)" %}',
                price: expressResponse.price,
                code: 'express',
                delay: expressResponse.delay
            });
        }
        
        if (methods.length === 0) {
            showNoShippingMessage();
            return;
        }
        
        // Sort methods by price (cheapest first)
        methods.sort((a, b) => a.price - b.price);
        
        // Display methods
        methodsList.innerHTML = '';
        methods.forEach(method => {
            const methodDiv = document.createElement('div');
            methodDiv.className = 'flex items-center justify-between p-3 border border-gray-200 rounded-lg';
            
            let priceHtml = `€${method.price.toFixed(2)}`;
            if (method.delay && method.delay > 0) {
                priceHtml += `<br><small class="text-gray-500">+${method.delay} {% trans "day(s) delay" %}</small>`;
            }
            
            methodDiv.innerHTML = `
                <div class="text-sm text-gray-700">${method.name}</div>
                <div class="text-sm font-medium text-gray-900">${priceHtml}</div>
            `;
            methodsList.appendChild(methodDiv);
        });
        
        // Show methods section
        methodsDisplay.style.display = 'block';
        
        // Show COD notice if applicable
        const showCod = codCountries.includes(country) && 
                       parseFloat(weight) <= 30 && 
                       methods.some(m => m.code === 'economy');
        codNotice.style.display = showCod ? 'flex' : 'none';
    }
    
    function showLoadingState() {
        methodsList.innerHTML = '<div class="text-center py-4 text-gray-500">{% trans "Loading shipping options..." %}</div>';
        methodsDisplay.style.display = 'block';
        codNotice.style.display = 'none';
    }
    
    function showNoShippingMessage() {
        methodsList.innerHTML = '<div class="text-center py-4 text-red-500">{% trans "Shipping not available to this country" %}</div>';
        methodsDisplay.style.display = 'block';
        codNotice.style.display = 'none';
    }
    
    function hideAllSections() {
        methodsDisplay.style.display = 'none';
        codNotice.style.display = 'none';
    }
});
</script>
