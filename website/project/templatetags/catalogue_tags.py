from django import template
from decimal import Decimal as D
from django.template.defaultfilters import floatformat
from django.conf import settings
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from oscar.core.loading import get_model

# Import Line model here if no circular import issues, otherwise keep inside the function.
from oscar.apps.basket.models import Line
# Import the currency filter function directly
from .custom_currency_filters import user_currency

register = template.Library()
Basket = get_model('basket', 'basket')

@register.filter
def get(dictionary, key):
    """
    Gets an item from a dictionary using its key
    Usage: {{ mydict|get:key_variable }}
    """
    if not dictionary:
        return 0
    return dictionary.get(key, 0)

def _calculate_basket_total(user):
    """Helper function to calculate the total using ORM."""
    return Line.objects.filter(
        basket__owner=user,
        basket__status='Open'
    ).annotate(
        line_total=ExpressionWrapper(
            F('price_incl_tax') * F('quantity'),
            output_field=DecimalField()
        )
    ).aggregate(
        total=Sum('line_total')
    )['total'] or D('0.00')

def _get_saved_products(user):
    """Helper function to get all saved products for a user in a single query."""
    if not user.is_authenticated:
        return set()

    try:
        # Get all product IDs from the saved basket in a single query
        saved_basket = Basket.saved.get(owner=user)
        return set(Line.objects.filter(basket=saved_basket).values_list('product_id', flat=True))
    except (Basket.DoesNotExist, Basket.MultipleObjectsReturned):
        return set()

@register.simple_tag(takes_context=True)
def get_saved_products(context):
    """
    Get all saved products for the current user and cache them in the request.
    Returns a set of product IDs that are in the user's saved basket.
    """
    request = context.get('request')

    if not request or not request.user.is_authenticated:
        return set()

    # Check cache first
    if hasattr(request, 'saved_products_cache'):
        return request.saved_products_cache

    # Get and cache saved products
    saved_products = _get_saved_products(request.user)
    request.saved_products_cache = saved_products

    return saved_products

@register.filter
def is_saved_product(product_id, saved_products):
    """
    Check if a product is in the set of saved products.
    Usage: {{ product.id|is_saved_product:saved_products }}
    """
    return product_id in saved_products

@register.simple_tag(takes_context=True)
def optimized_basket_total(context, currency_info=None):
    """
    Optimized tag to display basket total using Django ORM and request caching.
    Formats the value using the user_currency filter.
    Works for both authenticated and anonymous users.
    """
    request = context.get('request')
    total = D('0.00')

    if request:
        # Check cache first
        if hasattr(request, 'basket_total_cache'):
            total = request.basket_total_cache
        elif request.user.is_authenticated:
            # For authenticated users, calculate using the database query
            total = _calculate_basket_total(request.user)
            request.basket_total_cache = total
        else:
            # For anonymous users, get the total from the basket object
            # This uses the basket that's loaded by BasketMiddleware
            try:
                if hasattr(request, 'basket') and not request.basket.is_empty:
                    # Use the basket's total_incl_tax property
                    total = request.basket.total_incl_tax
                    request.basket_total_cache = total
            except Exception:
                # If there's any error, fall back to zero
                total = D('0.00')

    # Format the value using the imported filter function
    if currency_info:
        return user_currency(total, currency_info)
    else:
        # Default formatting if no currency info provided
        return floatformat(total, 2)

@register.simple_tag
def get_ebay_advertisement(pap_product_id):
    """
    Get eBay advertisement for a given vehicle part (PAP product) ID.
    
    Args:
        pap_product_id: The ID of the VehiclePart (PAP product)
        
    Returns:
        VehiclePartAdvertisment instance if exists, None otherwise
    """
    try:
        from project.apps.pap.models import VehiclePartAdvertisment
        
        ebay_ad = VehiclePartAdvertisment.objects.filter(
            vehicle_part_id=pap_product_id,
            shop=settings.SHOP_EBAY,
            ebay_listing_id__isnull=False,
            active=True
        ).first()
        
        return ebay_ad
    except Exception:
        return None