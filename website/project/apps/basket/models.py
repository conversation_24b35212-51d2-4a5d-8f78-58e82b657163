from decimal import Decimal as D

from django.conf import settings
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db.models.signals import pre_save
from django.dispatch import receiver

from oscar.apps.basket.abstract_models import AbstractBasket
from oscar.apps.payment.models import SourceType, Source
from oscar.apps.shipping.methods import FixedPrice
from oscar.core import prices
from oscar.core.loading import get_class, get_model

from project.accounts import get_branch
from project.apps.address.models import Country
from project.apps.order.models import Order
from project.apps.partner.models import Currency

class Basket(AbstractBasket):
    SHIPPING_ECONOMY = 'fixed-price-economy'
    SHIPPING_EXPRESS = 'fixed-price-express'
    SHIPPING_LOCAL_PICK_UP = 'local-pick-up'

    SHIPPING_CHOICES = [
        (SHIPPING_ECONOMY, _('Economy shipping (3-5 work days)')),
        (SHIPPING_EXPRESS, _('Express shipping (1-3 work days)')),
    ]

    SHIPPING_CHOICES_RU = [(SHIPPING_ECONOMY, _('Economy shipping (3-5 work days)'))]

    country = models.ForeignKey('address.Country', editable=False, null=True, default=None, on_delete=models.SET_NULL)
    user_currency = models.CharField(max_length=3, editable=False, default=settings.OSCAR_DEFAULT_CURRENCY)
    shipping_method = models.CharField(
        max_length=50, editable=False, choices=SHIPPING_CHOICES, default=SHIPPING_ECONOMY
    )
    notes = models.TextField(default='')
    branch = models.CharField(editable=False, max_length=10, blank=True, default='eu')
    date_frozen = models.DateTimeField(_("Date frozen"), null=True, blank=True, editable=False)

    # temporary order info, use it only for creating order from frozen basket
    order_total_incl_tax = models.DecimalField(_("Order total (inc. tax)"), decimal_places=2, max_digits=12, default=0)
    order_total_excl_tax = models.DecimalField(
        _("Order total (excl. tax)"), decimal_places=2, max_digits=12, default=0
    )
    order_shipping_incl_tax = models.DecimalField(
        _("Shipping charge (inc. tax)"), decimal_places=2, max_digits=12, default=0
    )
    order_shipping_excl_tax = models.DecimalField(
        _("Shipping charge (excl. tax)"), decimal_places=2, max_digits=12, default=0
    )
    order_shipping_address = models.ForeignKey(
        'order.ShippingAddress', null=True, blank=True, verbose_name=_("Shipping Address"), on_delete=models.SET_NULL
    )
    order_shipping_method = models.CharField(_("Shipping method"), max_length=128, null=True, blank=True)
    order_shipping_code = models.CharField(blank=True, max_length=128, default="")
    order_billing_address = models.ForeignKey(
        'order.BillingAddress', null=True, blank=True, verbose_name=_("Billing Address"), on_delete=models.SET_NULL
    )
    order_guest_email = models.EmailField(_("Guest email address"), null=True, blank=True)
    order_payment_method = models.CharField(blank=True, max_length=128, default="")

    def get_order_number(self):
        from project.apps.order.utils import OrderNumberGenerator

        return OrderNumberGenerator().order_number(self)

    @property
    def is_missing_stock(self):
        out_of_stock = False
        for line in self.all_lines():
            if line.stockrecord.net_stock_level <= 0:
                out_of_stock = True
        return out_of_stock

    @property
    def order_payment_reference(self):
        from project.apps.checkout.forms import PaymentForm
        from project.everypay.models import Payment as EveryPayPayment
        from webtopay.models import WebToPayResponse

        order_number = self.get_order_number()

        def _get_paysera_ref():
            try:
                transaction = WebToPayResponse.objects.get(orderid=order_number)
            except WebToPayResponse.DoesNotExist:
                return ''
            else:
                return transaction.requestid

        def _get_paypal_ref():
            return 'not available (check PayPal account)'

        def _get_everypay_ref():
            try:
                payment = EveryPayPayment.objects.get(order_number=order_number)
            except EveryPayPayment.DoesNotExist:
                return ''
            else:
                return payment.reference

        payment_ref_map = {
            PaymentForm.PAYMENT_METHOD_WTP: _get_paysera_ref,
            PaymentForm.PAYMENT_METHOD_PAYPAL: _get_paypal_ref,
            PaymentForm.PAYMENT_METHOD_EVERYPAY: _get_everypay_ref,
        }
        payment_method = self.order_payment_method.partition('__')[0]
        payment_ref = payment_ref_map.get(payment_method)
        if payment_ref:
            reference = payment_ref()
        else:
            reference = ''

        return reference

    def get_default_user_currency(self):
        branch = get_branch()
        if branch == 'eu':
            rcurrency = {'currency': settings.OSCAR_DEFAULT_CURRENCY, 'rate': '1.0000'}
        else:
            default_currency_code = settings.BRANCH_SETTINGS[branch]['currency']
            currency = Currency.objects.get(currency=default_currency_code)
            rcurrency = {'currency': default_currency_code, 'rate': '%s' % currency.rate}
        return rcurrency

    def get_user_currency(self):
        try:
            currency = Currency.objects.get(currency=self.user_currency)
        except Currency.DoesNotExist:
            currency = None
        except Currency.MultipleObjectsReturned:
            currency = Currency.objects.filter(currency=self.user_currency)[0]

        if currency:
            rcurrency = {'currency': currency.currency, 'rate': '%s' % currency.rate}
        else:
            rcurrency = self.get_default_user_currency()
        return rcurrency

    @property
    def currency(self):
        return settings.OSCAR_DEFAULT_CURRENCY  # self.get_user_currency()['currency']

    def is_vendor_valid(self, vendor):
        for l in self.all_lines():
            if vendor != l.product.owner:
                return False
        return True

    def get_vendor(self):
        for l in self.all_lines():
            return l.product.owner

    def is_shipping_required(self):
        return True

    def is_shipping_available(self):
        shipping_preform, shipping_price, country = self.get_shipping_info()
        return shipping_preform is not None

    def get_shipping_info(self, country=None, shipping_method=None):
        branch = get_branch()
        try:
            branch_country = Country.objects.get(iso_3166_1_a2__iexact=branch)
        except Country.DoesNotExist:
            branch_country = Country.objects.get(iso_3166_1_a2='LT')

        item_charge = D('0.00')
        extra_item_charge = D('0.00')
        shipping_preform = None
        main_product = None
        country = country or self.country or branch_country
        shipping_method = shipping_method or self.shipping_method

        # Get all lines and products in one query
        lines = self.all_lines().select_related('product')
        
        # First pass - find main product with highest shipping charge
        for line in lines:
            shipping_preform = line.product.get_shipping_preform(country)
            if shipping_preform:
                if shipping_method == self.SHIPPING_ECONOMY:
                    if shipping_preform.price_per_item > item_charge:
                        item_charge = shipping_preform.price_per_item
                        extra_item_charge = shipping_preform.price_per_extra_item
                        main_product = line.product
                if shipping_method == self.SHIPPING_EXPRESS:
                    if shipping_preform.price_per_item_express > item_charge:
                        item_charge = shipping_preform.price_per_item_express
                        extra_item_charge = shipping_preform.price_per_extra_item_express
                        main_product = line.product

        # Second pass - calculate total charge
        charge = D('0.00')
        for line in lines:
            items_number = line.quantity
            shipping_preform = line.product.get_shipping_preform(country)
            if shipping_preform:
                if line.product == main_product:
                    charge += item_charge + (extra_item_charge * (items_number - 1))
                else:
                    if shipping_method == self.SHIPPING_ECONOMY:
                        charge += shipping_preform.price_per_extra_item * items_number
                    if shipping_method == self.SHIPPING_EXPRESS:
                        charge += shipping_preform.price_per_extra_item_express * items_number

        return (shipping_preform, charge, country)

    def get_shipping_delay(self, shipping_country_code):
        delay = 0
        for line in self.all_lines():
            shipping_delay_for_product = line.product.get_shipping_delay(shipping_country_code)
            if shipping_delay_for_product > delay:
                delay = shipping_delay_for_product
        return delay

    def save_temp_order_info(self, **kwargs):
        self.order_total_incl_tax = kwargs['order_total'].incl_tax
        self.order_total_excl_tax = kwargs['order_total'].excl_tax
        self.order_shipping_incl_tax = kwargs['shipping_charge'].incl_tax
        self.order_shipping_excl_tax = kwargs['shipping_charge'].excl_tax
        self.order_shipping_address = kwargs['shipping_address']
        self.order_shipping_method = kwargs['shipping_method'].name
        self.order_shipping_code = kwargs['shipping_method'].code
        self.order_guest_email = (
            kwargs['order_kwargs']['guest_email'] if 'guest_email' in kwargs['order_kwargs'] else None
        )
        self.order_billing_address = kwargs['billing_address']
        self.branch = kwargs['branch']
        self.save()

    def place_order_from_frozen_basket(self, reference='', payment_source_type=''):
        """Place order from frozen basket"""

        if self.lines.all().count() and Order.objects.filter(basket_id=self.id).count() == 0:
            OrderCreator = get_class('order.utils', 'OrderCreator')
            Selector = get_class('partner.strategy', 'Selector')
            PaymentEvent = get_model('order', 'PaymentEvent')
            PaymentEventType = get_model('order', 'PaymentEventType')
            PaymentEventQuantity = get_model('order', 'PaymentEventQuantity')

            payment_source_type = self.order_payment_method or payment_source_type
            if payment_source_type.find('__') > -1:
                payment_source_type = payment_source_type.rpartition('__')[-1]

            reference = self.order_payment_reference or reference

            self.strategy = Selector().strategy()
            vendor_owner = self.lines.all()[0].product.owner

            total = prices.Price(
                currency='EUR', excl_tax=self.order_total_excl_tax, incl_tax=self.order_total_incl_tax
            )

            shipping_method = FixedPrice(self.order_shipping_excl_tax, self.order_shipping_incl_tax)
            shipping_method.code = self.order_shipping_code
            shipping_method.name = self.order_shipping_method

            shipping_charge = shipping_method.calculate(self)

            order = OrderCreator().place_order(
                user=self.owner,
                basket=self,
                shipping_address=self.order_shipping_address,
                shipping_method=shipping_method,
                shipping_charge=shipping_charge,
                total=total,
                billing_address=self.order_billing_address,
                status='Paid',
                request=None,
                **{'guest_email': self.order_guest_email if self.order_guest_email else ''}
            )

            order.owner = vendor_owner
            order.user_currency = self.user_currency
            order.from_frozen_basket = True
            order.branch = self.branch
            order.save()
            self.submit()

            amount = total.incl_tax
            source_type, is_created = SourceType.objects.get_or_create(name=payment_source_type)
            event_type, __ = PaymentEventType.objects.get_or_create(name='Settled')
            source = Source.objects.create(
                source_type=source_type, currency='EUR', amount_allocated=amount, amount_debited=amount, order=order
            )
            event = PaymentEvent.objects.create(event_type=event_type, amount=amount, reference=reference, order=order)

            for line in order.lines.all():
                line.set_status('Paid')
                PaymentEventQuantity.objects.create(event=event, line=line, quantity=line.quantity)

            order.create_invoice()
            order.send_confirmation_message()
            return order
        else:
            return None


@receiver(pre_save, sender=Basket)
def update_date_frozen(sender, instance, **kwargs):
    """
    Signal handler to update date_frozen when basket status changes.
    Sets date_frozen when status becomes FROZEN, clears it otherwise.
    """
    try:
        old_instance = sender.objects.get(pk=instance.pk)
        # If status is changing to FROZEN
        if old_instance.status != instance.status:
            if instance.status == Basket.FROZEN:
                instance.date_frozen = timezone.now()
            else:
                instance.date_frozen = None
    except sender.DoesNotExist:
        # New instance being created
        if instance.status == Basket.FROZEN:
            instance.date_frozen = timezone.now()


from oscar.apps.basket.models import *
