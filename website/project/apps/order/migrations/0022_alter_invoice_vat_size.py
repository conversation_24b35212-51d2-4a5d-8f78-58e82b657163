# Generated by Django 3.2.20 on 2024-10-01 08:47

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0021_alter_invoice_vat_size_for_oss'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='invoice',
            name='vat_size',
            field=models.DecimalField(choices=[(Decimal('21.00'), '21.00'), (Decimal('16.00'), '16.00'), (Decimal('19.00'), '19.00'), (Decimal('0.00'), '0.00')], decimal_places=2, default=Decimal('21.00'), max_digits=7, verbose_name='VAT size, %'),
        ),
    ]
