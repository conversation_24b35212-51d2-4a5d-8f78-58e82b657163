import logging
from decimal import Decimal as D
from datetime import datetime
from urllib.parse import quote

from django import http
from django.conf import settings
from django.contrib import messages

# from django.contrib.auth import authenticate, login as auth_login
from django.contrib.auth import login
from django.db import IntegrityError
from django.forms import ValidationError
from django.shortcuts import redirect
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from django.core.exceptions import ObjectDoesNotExist
from django.urls import reverse, reverse_lazy
from django.http import HttpResponseRedirect, HttpResponse
from django.middleware.csrf import get_token
from django.template import loader
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.utils.translation import gettext as _, get_language
from django.views import generic

from ipware import get_client_ip
from oscar.apps.checkout import views, signals
from oscar.apps.customer.forms import generate_username
from oscar.apps.customer.mixins import RegisterUserMixin
from oscar.apps.payment import models
from oscar.apps.shipping.methods import NoShippingRequired
from oscar.core.loading import get_class, get_classes, get_model

from project.apps.shop_settings.models import ShopSettings

from project.everypay.helpers import EveryPayHelper

from project.apps.shipping.repository import Repository, NotAvailable

from .forms import RegistrationForm, PaymentForm, ShippingAddressForm, UserAddressForm
from webtopay.forms import WebToPaymentForm

ShippingAddressForm, ShippingMethodForm, GatewayForm = get_classes(
    "checkout.forms", ["ShippingAddressForm", "ShippingMethodForm", "GatewayForm"]
)
pre_payment, post_payment = get_classes("checkout.signals", ["pre_payment", "post_payment"])
RedirectRequired, UnableToTakePayment, PaymentError = get_classes(
    "payment.exceptions", ["RedirectRequired", "UnableToTakePayment", "PaymentError"]
)
OrderPlacementMixin = get_class("checkout.mixins", "OrderPlacementMixin")
UnableToPlaceOrder = get_class("order.exceptions", "UnableToPlaceOrder")
CheckoutSessionMixin = get_class("checkout.session", "CheckoutSessionMixin")
CheckoutSessionData = get_class("checkout.utils", "CheckoutSessionData")

ShippingAddress = get_model("order", "ShippingAddress")
UserAddress = get_model("address", "UserAddress")
BillingAddress = get_model("order", "BillingAddress")
Country = get_model("address", "Country")
Selector = get_class("partner.strategy", "Selector")
Basket = get_model("basket", "Basket")

# Standard logger for checkout events
logger = logging.getLogger("project.apps.checkout")

from .mixins import CustomOrderPlacementMixin


class IndexView(CheckoutSessionMixin, generic.FormView):
    """
    First page of the checkout.  We prompt user to either sign in, or
    to proceed as a guest (where we still collect their email address).
    """

    template_name = "oscar/checkout/gateway.html"
    form_class = GatewayForm
    success_url = reverse_lazy("checkout:shipping-address")
    pre_conditions = ["check_basket_is_not_empty", "check_basket_is_valid"]

    def get(self, request, *args, **kwargs):
        # We redirect immediately to shipping address stage if the user is
        # signed in.
        if request.user.is_authenticated:
            # We raise a signal to indicate that the user has entered the
            # checkout process so analytics tools can track this event.
            signals.start_checkout.send_robust(sender=self, request=request)
            return self.get_success_response()
        return super().get(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        email = self.checkout_session.get_guest_email()
        if email:
            kwargs["initial"] = {
                "username": email,
            }
        return kwargs

    def form_valid(self, form):
        if form.is_guest_checkout() or form.is_new_account_checkout():
            email = form.cleaned_data["username"]
            self.checkout_session.set_guest_email(email)

            # We raise a signal to indicate that the user has entered the
            # checkout process by specifying an email address.
            signals.start_checkout.send_robust(sender=self, request=self.request, email=email)

            if form.is_new_account_checkout():
                messages.info(
                    self.request,
                    _("Create your account and then you will be redirected " "back to the checkout process"),
                )
                self.success_url = "%s?next=%s&email=%s" % (
                    reverse("customer:register"),
                    reverse("checkout:shipping-address"),
                    quote(email),
                )
        else:
            user = form.get_user()
            login(self.request, user)

            # We raise a signal to indicate that the user has entered the
            # checkout process.
            signals.start_checkout.send_robust(sender=self, request=self.request)

        return redirect(self.get_success_url())

    def get_success_response(self):
        return redirect(self.get_success_url())


# ================
# SHIPPING ADDRESS
# ================


class ShippingAddressView(CheckoutSessionMixin, generic.FormView):
    """
    Determine the shipping address for the order.

    The default behaviour is to display a list of addresses from the users's
    address book, from which the user can choose one to be their shipping
    address.  They can add/edit/delete these USER addresses.  This address will
    be automatically converted into a SHIPPING address when the user checks
    out.

    Alternatively, the user can enter a SHIPPING address directly which will be
    saved in the session and later saved as ShippingAddress model when the
    order is successfully submitted.
    """

    template_name = "oscar/checkout/shipping_address.html"
    form_class = ShippingAddressForm
    success_url = reverse_lazy("checkout:shipping-method")
    pre_conditions = [
        "check_basket_is_not_empty",
        "check_basket_is_valid",
        "check_user_email_is_captured",
    ]
    skip_conditions = ["skip_unless_basket_requires_shipping"]

    def get_initial(self):
        """Return initial form data from session"""

        if self.checkout_session.is_shipping_address_set():
            shipping_fields = self.checkout_session.new_shipping_address_fields()
            if shipping_fields:

                initial = {
                    k: v
                    for k, v in shipping_fields.items()
                    if not k.startswith('billing_')
                    and k not in ['is_billing_address_same_as_shipping', 'billing_country']
                }

                # Convert country_id to Country object
                if 'country_id' in shipping_fields:
                    initial['country'] = Country.objects.get(iso_3166_1_a2=shipping_fields['country_id'])
                
                if 'billing_country_id' in shipping_fields:
                    initial['billing_country'] = Country.objects.get(iso_3166_1_a2=shipping_fields['billing_country_id'])

                initial['is_billing_address_same_as_shipping'] = shipping_fields.get(
                    'is_billing_address_same_as_shipping', True
                )
                return initial

        return {}

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data(**kwargs)
        if self.request.user.is_authenticated:
            # Look up address book data
            ctx["addresses"] = self.get_available_addresses()
        return ctx

    def get_available_addresses(self):
        # Include only addresses where the country is flagged as valid for
        # shipping. Also, use ordering to ensure the default address comes
        # first.
        return self.request.user.addresses.filter(country__is_shipping_country=True).order_by(
            "-is_default_for_shipping"
        )

    def ship_to_user_address(self, address):
        """Save selected address"""

        phone_number = str(address.phone_number) if hasattr(address, 'phone_number') else ''

        address_fields = {
            'first_name': address.first_name or '',
            'last_name': address.last_name or '',
            'line1': address.line1 or '',
            'line2': address.line2 or '',
            'line3': address.line3 or '',
            'line4': address.line4 or '',
            'state': address.state or '',
            'postcode': address.postcode or '',
            'country_id': address.country.code,
            'phone_number': phone_number,
            'company_name': address.company_name or '',
            'company_code': address.company_code or '',  # Add company code
            'vat_number': address.vat_number or '',  # Add VAT number
        }
        

        # Save to session using Oscar's method
        self.checkout_session.ship_to_user_address(address)

        # Explicitly save address fields to session
        self.checkout_session._set('shipping', 'address_fields', address_fields)

        # Update basket with country_id for consistency
        if hasattr(address, 'country'):
            self.request.basket.country_id = address.country.code
            self.request.basket.save()

        if self.request.user.is_authenticated:
            try:
                default_billing_address = self.request.user.addresses.get(is_default_for_billing=True)
                self.checkout_session.bill_to_user_address(default_billing_address)
            except UserAddress.DoesNotExist:
                # If no default billing address exists, use shipping address as billing
                self.checkout_session.bill_to_shipping_address()

        return redirect(self.get_success_url())

    def post(self, request, *args, **kwargs):

        if 'address_id' in request.POST:
            address = UserAddress._default_manager.get(pk=request.POST['address_id'])
            return self.ship_to_user_address(address)

        return super().post(request, *args, **kwargs)

    def form_invalid(self, form):
        """
        If the form is invalid, we want to show an error message.
        """
        # Add a single error message with all field errors
        error_list = []
        for field, errors in form.errors.items():
            error_list.extend(errors)
        
        messages.error(
            self.request,
            mark_safe("<br>".join(error_list)),
            extra_tags='danger'  # This will add alert-danger class
        )
        return super().form_invalid(form)

    def form_valid(self, form):
        # Process shipping address
        shipping_fields = dict(
            (k, v)
            for (k, v) in form.instance.__dict__.items()
            if not k.startswith("_") and not k.startswith("billing_")
        )
        
        # Save country as ISO code
        shipping_fields['country_id'] = form.instance.country.code
        if 'country' in shipping_fields:
            del shipping_fields['country']
            
        self.checkout_session.ship_to_new_address(shipping_fields)

        # Update basket country - use ISO code for consistency
        if hasattr(self.request, 'basket') and self.request.basket:
            self.request.basket.country_id = form.instance.country.code
            self.request.basket.save()

        # Process billing address
        is_billing_same = form.cleaned_data.get('is_billing_address_same_as_shipping', True)
        if is_billing_same:
            self.checkout_session.bill_to_shipping_address()
        else:
            # Create temporary billing address instance to use __dict__
            billing_instance = ShippingAddress()
            for field_name, value in form.cleaned_data.items():
                if field_name.startswith('billing_'):
                    clean_name = field_name.replace('billing_', '')
                    setattr(billing_instance, clean_name, value)

            # Extract fields using __dict__ like with shipping
            billing_fields = dict(
                (k, v) 
                for (k, v) in billing_instance.__dict__.items() 
                if not k.startswith("_")
            )
            
            # Save billing country as ISO code
            billing_fields['country_id'] = billing_instance.country.code
            if 'country' in billing_fields:
                del billing_fields['country']

            self.checkout_session.bill_to_new_address(billing_fields)

        # Save addresses to address book ONLY if user is authenticated
        if self.request.user.is_authenticated:
            # Check if this is the first address for the user
            is_first_address = not self.request.user.addresses.exists()

            # Create shipping address in address book - convert ISO to Country for UserAddress
            try:
                shipping_address_data = shipping_fields.copy()
                shipping_address_data['country'] = Country.objects.get(iso_3166_1_a2=shipping_fields['country_id'])
                if 'country_id' in shipping_address_data:
                    del shipping_address_data['country_id']
                    
                # Try to find existing address first
                address_hash = UserAddress(**shipping_address_data).generate_hash()
                existing_address = UserAddress.objects.filter(
                    user=self.request.user,
                    hash=address_hash
                ).first()

                if existing_address:
                    user_shipping_address = existing_address
                else:
                    user_shipping_address = UserAddress.objects.create(user=self.request.user, **shipping_address_data)

            except Exception as e:
                logger.error(f"Error saving shipping address: {str(e)}")
                raise

            # If this is the first address, set it as default for both shipping and billing
            if is_first_address:
                user_shipping_address.is_default_for_shipping = True
                user_shipping_address.is_default_for_billing = True
                user_shipping_address.save()

            # If billing address is different, save it too
            if not is_billing_same:
                billing_fields = dict(
                    (k.replace('billing_', ''), v)
                    for (k, v) in form.cleaned_data.items()
                    if k.startswith('billing_') and v
                )
                
                # Convert ISO to Country for UserAddress
                billing_fields['country'] = Country.objects.get(iso_3166_1_a2=billing_instance.country.code)

                # Try to find existing billing address first
                billing_hash = UserAddress(**billing_fields).generate_hash()
                existing_billing = UserAddress.objects.filter(
                    user=self.request.user,
                    hash=billing_hash
                ).first()

                if existing_billing:
                    user_billing_address = existing_billing
                else:
                    user_billing_address = UserAddress.objects.create(
                        user=self.request.user, 
                        **billing_fields
                    )

                # If this is the first address, set billing address as default too
                if is_first_address:
                    user_billing_address.is_default_for_billing = True
                    user_billing_address.save()
        else:
            logger.debug("Skipping address book save for anonymous user")

        return redirect(self.get_success_url())


class UserAddressUpdateView(CheckoutSessionMixin, generic.UpdateView):
    """
    Update a user address
    """

    template_name = "oscar/checkout/user_address_form.html"
    form_class = UserAddressForm
    success_url = reverse_lazy("checkout:shipping-address")

    def get_queryset(self):
        return self.request.user.addresses.all()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_success_url(self):
        messages.info(self.request, _("Address saved"))
        return super().get_success_url()


class UserAddressDeleteView(CheckoutSessionMixin, generic.DeleteView):
    """
    Delete an address from a user's address book.
    """

    template_name = "oscar/checkout/user_address_delete.html"
    success_url = reverse_lazy("checkout:shipping-address")

    def get_queryset(self):
        return self.request.user.addresses.all()

    def get_success_url(self):
        messages.info(self.request, _("Address deleted"))
        return super().get_success_url()


class AddressChangeStatusView(generic.RedirectView):
    """Sets an address as default_for_(billing|shipping) during checkout"""

    url = reverse_lazy('checkout:shipping-address')
    permanent = False

    def get(self, request, *args, pk=None, action=None, **kwargs):
        address = self.request.user.addresses.get(pk=pk)

        if action == "default_for_shipping" and address.country.is_shipping_country:
            self.request.user.addresses.filter(is_default_for_shipping=True).update(is_default_for_shipping=False)
            address.is_default_for_shipping = True
            address.save()
        elif action == "default_for_billing":
            self.request.user.addresses.filter(is_default_for_billing=True).update(is_default_for_billing=False)
            address.is_default_for_billing = True
            address.save()
        else:
            messages.error(request, _("Invalid action"))

        return super().get(request, *args, **kwargs)


class PaymentFormMixin(object):
    """Payment form mixin"""

    def dispatch(self, request, *args, **kwargs):
        self.payment_form = self.get_payment_form()
        return super(PaymentFormMixin, self).dispatch(request, *args, **kwargs)

    def get_payment_form(self):
        if self.request.method == "POST":
            data = self.request.POST
        else:
            data = None

        basket = self.request.basket
        shipping_address = self.get_shipping_address(basket)
        shipping_method = self.get_shipping_method(basket, shipping_address)
        if data:
            payment_form = PaymentForm(
                data,
                request=self.request,
                basket=basket,
                shipping_method=shipping_method,
                shipping_address=shipping_address,
            )
        else:
            payment_form = PaymentForm(
                # initial={'payment_method': payment_method if payment_method else 'paypal'},
                basket=basket,
                request=self.request,
                shipping_method=shipping_method,
                shipping_address=shipping_address,
            )
        return payment_form

    # def get_context_data(self, **kwargs):
    #     ctx = super(PaymentFormMixin, self).get_context_data(**kwargs)
    #     ctx["payment_form"] = kwargs.get("payment_form", self.get_payment_form())
    #     return ctx


class ShippingMethodView(CheckoutSessionMixin, generic.FormView):
    template_name = "oscar/checkout/shipping_methods.html"
    form_class = ShippingMethodForm
    pre_conditions = [
        "check_basket_is_not_empty",
        "check_basket_is_valid",
        "check_user_email_is_captured",
    ]
    success_url = reverse_lazy("checkout:payment-method")

    def get(self, request, *args, **kwargs):
        if not request.basket.is_shipping_required():
            self.checkout_session.use_shipping_method(NoShippingRequired().code)
            return redirect(self.get_success_url())

        # Cache shipping address
        shipping_address = self.get_shipping_address(request.basket)
        if shipping_address:
            self._shipping_address = shipping_address
        else:
            messages.error(request, _("Please choose a shipping address"))
            return redirect("checkout:shipping-address")

        # Check shipping availability for each product
        unavailable_products = []
        for line in request.basket.all_lines():
            shipping_preform = line.product.get_shipping_preform(self._shipping_address.country)
            if not shipping_preform:
                unavailable_products.append(line.product)

        # If any product cannot be shipped, show error and redirect back
        if unavailable_products:
            product_names = ", ".join([p.get_title() for p in unavailable_products])
            logger.info(f"Blocked checkout - Products that cannot be shipped to {self._shipping_address.country.name}: {product_names}")
            messages.add_message(
                request, 
                messages.ERROR, 
                _("The following products cannot be shipped to %(country)s: %(products)s") % {
                    'country': self._shipping_address.country.name,
                    'products': product_names
                },
                extra_tags='danger'
            )
            return redirect("checkout:shipping-address")

        # Get shipping methods for the entire basket
        methods = Repository().get_shipping_methods(
            basket=request.basket, user=request.user, shipping_addr=self._shipping_address, request=request
        )

        # Store methods in instance for the template context
        self._methods = methods

        # If only one valid method (not NotAvailable), set it and redirect to payment
        if len(methods) == 1 and not isinstance(methods[0], NotAvailable):
            messages.info(request, _("There was only one shipping method available, it was selected automatically"))
            self.checkout_session.use_shipping_method(methods[0].code)
            return redirect(self.get_success_url())

        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        kwargs = super().get_context_data(**kwargs)
        kwargs['methods'] = self._methods
        return kwargs

    def get_shipping_address(self, basket):
        if not self.checkout_session.is_shipping_address_set():
            logger.debug("No shipping address set in session")
            return None

        addr_data = self.checkout_session._get('shipping', 'address_fields')
        if not addr_data:
            addr_data = self.checkout_session._get('shipping', 'new_address_fields')
        else:
            pass

        if not addr_data:
            logger.debug("No address data found in session")
            return None

        # Convert country_id to Country object only when creating ShippingAddress
        country_id = addr_data.get('country_id')
        if country_id:
            try:
                country = Country.objects.get(iso_3166_1_a2=country_id)
                # Instead of modifying addr_data, create a new dict for ShippingAddress
                shipping_addr_data = addr_data.copy()
                shipping_addr_data['country'] = country
                return ShippingAddress(**shipping_addr_data)
            except Country.DoesNotExist:
                logger.warning(f"Country not found for id: {country_id}")
                return None
        
        return ShippingAddress(**addr_data)

    def post(self, request, *args, **kwargs):
        method_code = request.POST.get('method_code')
        if method_code:
            self.checkout_session.use_shipping_method(method_code)
            return redirect(self.get_success_url())

        return super().post(request, *args, **kwargs)


class UserAddressUpdateView(views.UserAddressUpdateView):
    """Update a user address"""

    template_name = "oscar/checkout/user_address_form.html"

    form_class = UserAddressForm


class PaymentDetailsView(CustomOrderPlacementMixin, views.PaymentDetailsView):
    """
    Combined payment method selection and preview view.
    Extends Oscar's default PaymentDetailsView.
    """

    pre_conditions = [
        'check_basket_is_not_empty',
        'check_basket_is_valid',
        'check_user_email_is_captured',
        'check_shipping_data_is_captured',
    ]

    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data(**kwargs)
        basket = self.request.basket

        # Add payment form if we're not in preview mode
        if not self.preview:
            payment_form = PaymentForm(
                request=self.request,
                basket=basket,
                shipping_method=self.get_shipping_method(basket),
                shipping_address=self.get_shipping_address(basket),
            )
            ctx['payment_form'] = payment_form
            
            # Add NeoPay rules to context
            ctx['neopay_rules'] = payment_form.get_neopay_rules()

        # Get payment method from session
        payment_method = self.checkout_session.payment_method()
        if payment_method:
            if self.preview:
                # Preview mode - get payment method name
                if payment_method.startswith('neopay__'):
                    # For NeoPay, get the bank name from the payment form
                    payment_form = PaymentForm(
                        request=self.request,
                        basket=basket,
                        shipping_method=self.get_shipping_method(basket),
                        shipping_address=self.get_shipping_address(basket),
                    )
                    # Get bank name from NeoPay methods
                    bank_code = payment_method.split('__')[1]
                    neopay_methods = payment_form.get_neopay_methods()
                    bank_name = next(
                        (bank['name'] for bank in neopay_methods if bank['code'] == bank_code), 
                        payment_method
                    )
                    ctx['payment_method'] = bank_name
                else:
                    # For other payment methods, use the standard mapping
                    ctx['payment_method'] = PaymentForm.PAYMENT_METHOD_MAP.get(payment_method, payment_method)
            else:
                # Payment details view - full payment form logic
                payment_form = self.get_payment_form()
                ctx['payment_method'] = payment_form.PAYMENT_METHOD_MAP.get(payment_method, payment_method)

        return ctx

    def get_payment_form(self, *args, **kwargs):
        """Get the payment form with proper initialization"""
        basket = self.request.basket
        shipping_address = self.get_shipping_address(basket)

        form_kwargs = {
            'request': self.request,
            'basket': basket,
            'shipping_method': self.get_shipping_method(basket),
            'shipping_address': shipping_address,
        }

        if self.request.method == 'POST':
            return PaymentForm(self.request.POST, **form_kwargs)
        return PaymentForm(**form_kwargs)

    no_redirect_payments = [
        PaymentForm.PAYMENT_METHOD_CASH,
        PaymentForm.PAYMENT_METHOD_DPD_CASH,
        PaymentForm.PAYMENT_METHOD_BANK_TRANSFER,
    ]

    def get_pre_conditions(self, request):
        if self.preview:
            # The preview view needs to ensure payment information has been
            # correctly captured.
            return self.pre_conditions + ["check_payment_data_is_captured"]
        return super(PaymentDetailsView, self).get_pre_conditions(request)

    def get_skip_conditions(self, request):
        if not self.preview:
            # Payment details should only be collected if necessary
            return ["skip_unless_payment_is_required"]
        return super(PaymentDetailsView, self).get_skip_conditions(request)

    def post(self, request, *args, **kwargs):
        # Save notes to session if provided
        if 'notes' in request.POST:
            shipping_address = self.get_shipping_address(request.basket)
            if shipping_address:
                address_fields = dict((k, v) for (k, v) in shipping_address.__dict__.items() if not k.startswith('_'))
                # Update notes
                address_fields['notes'] = request.POST.get('notes')
                # Save updated address to session
                self.checkout_session.ship_to_new_address(address_fields)

        # Save payment method to session if it's in the POST data
        payment_method = request.POST.get('payment_method')
        if payment_method:
            self.checkout_session.pay_by(payment_method)

        return super().post(request, *args, **kwargs)

    def handle_place_order_submission(self, request):
        """
        Handle a request to place an order.
        """
        # Get payment method from session instead of form
        payment_method = self.checkout_session.payment_method()
        logger.debug(f"Payment method from session: {payment_method}")

        if not payment_method:
            messages.error(request, _("Please select a payment method"))
            return HttpResponseRedirect(reverse("checkout:payment-method"))

        # If this is PayPal payment, redirect to PayPal checkout
        if payment_method and payment_method.startswith(PaymentForm.PAYMENT_METHOD_PAYPAL):
            basket = self.request.basket
            
            # Build submission to get all order information
            submission = self.build_submission(
                payment_kwargs={
                    'payment_method': payment_method,
                }
            )
            
            # Generate order number
            order_number = self.generate_order_number(basket)
            logger.info(
                "Order #%s: preparing PayPal checkout for basket #%d (branch: %s)",
                order_number,
                basket.id,
                self.request.branch.branch
            )
            
            # Save basket state with all order information (same as in submit method)
            basket_state = {
                "order_number": order_number,
                "shipping_address": self.create_shipping_address(self.request.user, submission['shipping_address']),
                "billing_address": self.create_billing_address(submission['user'], submission['billing_address'], submission['shipping_address']),
                "shipping_method": submission['shipping_method'],
                "shipping_charge": submission['shipping_charge'],
                "order_total": submission['order_total'],
                "payment_kwargs": submission['payment_kwargs'],
                "order_kwargs": submission['order_kwargs'],
                "branch": self.request.branch.branch,
            }
            
            # Save temp order info to preserve state for admin panel
            basket.save_temp_order_info(**basket_state)
            
            # Save payment method to basket for admin visibility
            basket.order_payment_method = payment_method
            basket.save()
            
            # Only freeze if not already frozen
            if basket.status != Basket.FROZEN:
                basket.freeze()
                logger.debug(f"Basket {basket.id} frozen for PayPal checkout with order info saved (branch: {self.request.branch.branch})")
            else:
                logger.debug(f"Basket {basket.id} already frozen (status: {basket.status}, branch: {self.request.branch.branch})")
            
            # Ensure basket is properly saved to session - especially important for DE branch
            self.checkout_session.set_submitted_basket(basket)
            # Also store basket ID in multiple session keys for redundancy
            self.request.session['anonymous_basket_id'] = basket.id
            self.request.session['paypal_basket_id'] = basket.id  # Additional fallback for PayPal
            self.request.session.modified = True  # Force session save
            
            logger.debug(f"Redirecting to PayPal checkout for order {order_number}, basket {basket.id} (status: {basket.status}, branch: {self.request.branch.branch})")
            logger.debug(f"Session basket IDs - submitted: {self.checkout_session.get_submitted_basket_id()}, anonymous: {self.request.session.get('anonymous_basket_id')}, paypal: {self.request.session.get('paypal_basket_id')}")

            return HttpResponseRedirect(reverse("paypal_checkout:checkout"))

        # Save shipping notes if provided
        shipping_notes = request.POST.get('notes')

        # Build submission dict
        submission = self.build_submission(
            payment_kwargs={
                'payment_method': payment_method,
            }
        )

        # Add shipping notes to shipping address
        if shipping_notes and submission.get('shipping_address'):
            submission['shipping_address'].notes = shipping_notes
            submission['shipping_address'].save()
            logger.debug(f"Notes saved to shipping address: {shipping_notes}")

        logger.debug(f"Submission: {submission}")

        # Remove surcharges if present
        if 'surcharges' in submission:
            del submission['surcharges']

        # Submit the order
        return self.submit(**submission)

    def handle_payment_details_submission(self, request):
        """
        Handle a request to submit payment details.
        """

        payment_form = self.get_payment_form()
        if not payment_form.is_valid():
            return self.render_payment_details(request, payment_form=payment_form)

        # Save payment method to session
        payment_method = payment_form.cleaned_data.get('payment_method', None)
        if payment_method:
            self.checkout_session.pay_by(payment_method)

        # Always render preview first, regardless of payment method
        return self.render_preview(request)

    def render_preview(self, request, **kwargs):
        """
        Show a preview of the order.

        If sensitive data was submitted on the payment details page, you will
        need to pass it back to the view here so it can be stored in hidden
        form inputs.  This avoids ever writing the sensitive data to disk.
        """
        self.preview = True
        ctx = self.get_context_data(**kwargs)
        return self.render_to_response(ctx)

    def render_payment_details(self, request, **kwargs):
        """
        Show the payment details page

        This method is useful if the submission from the payment details view
        is invalid and needs to be re-rendered with form errors showing.
        """
        self.preview = False
        ctx = self.get_context_data(**kwargs)
        return self.render_to_response(ctx)

    def get_default_billing_address(self):
        """
        Return default billing address for user

        This is useful when the payment details view includes a billing address
        form - you can use this helper method to prepopulate the form.

        Note, this isn't used in core oscar as there is no billing address form
        by default.
        """
        if not self.request.user.is_authenticated:
            return None
        try:
            return self.request.user.addresses.get(is_default_for_billing=True)
        except UserAddress.DoesNotExist:
            return None

    def handle_successful_order(self, order):
        response = super().handle_successful_order(order)

        if order.status == "Paid":
            for line in order.lines.all():
                line.set_status("Paid")
            order.create_invoice()

        return response

    def submit(
        self,
        user,
        basket,
        shipping_address,
        shipping_method,
        shipping_charge,
        billing_address,
        order_total,
        payment_kwargs=None,
        order_kwargs=None,
        surcharges=None,
    ):
        """
        Submit a basket for order placement.
        """
        if basket.is_missing_stock:
            logger.info(
                "Rejecting order process for basket #%d due to low stock level",
                basket.id,
            )
            url = reverse("basket:summary")
            return HttpResponseRedirect(url)

        if not shipping_method:
            logger.info(
                "Rejecting order process for basket #%d due to missing shipping method",
                basket.id,
            )
            messages.error(
                self.request,
                _("Sorry. This order cannot be submitted. Please try again."),
            )
            url = self.request.META.get("HTTP_REFERER", reverse("basket:summary"))
            return HttpResponseRedirect(url)

        logger.debug(f"Starting order submission for basket {basket.id}")

        if payment_kwargs is None:
            payment_kwargs = {}
        if order_kwargs is None:
            order_kwargs = {}
        if surcharges is None:
            surcharges = []

        payment_kwargs.update(
            {
                'shipping_address': shipping_address,
                'billing_address': billing_address,
                'shipping_method': shipping_method,
            }
        )

        # Taxes must be known at this point
        assert basket.is_tax_known, "Basket tax must be set before a user can place an order"
        assert shipping_charge.is_tax_known, "Shipping charge tax must be set before a user can place an order"

        payment_method = payment_kwargs["payment_method"]
        logger.debug(f"Payment method from kwargs: {payment_method}")

        if basket.is_empty:
            messages.error(self.request, _("This order cannot be submitted as the basket is empty"))
            url = self.request.META.get("HTTP_REFERER", reverse("basket:summary"))
            return HttpResponseRedirect(url)

        # Generate order number
        order_number = self.generate_order_number(basket)
        logger.info(
            "Order #%s: beginning submission process for basket #%d",
            order_number,
            basket.id,
        )

        # Save basket state and freeze if needed
        basket_state = {
            "order_number": order_number,
            "shipping_address": self.create_shipping_address(self.request.user, shipping_address),
            "billing_address": self.create_billing_address(user, billing_address, shipping_address),
            "shipping_method": shipping_method,
            "shipping_charge": shipping_charge,
            "order_total": order_total,
            "payment_kwargs": payment_kwargs,
            "order_kwargs": order_kwargs,
            "branch": self.request.branch.branch,
        }

        # Always save temp order info to preserve state
        basket.save_temp_order_info(**basket_state)

        # Only freeze if not already frozen
        if basket.status == Basket.OPEN:
            logger.debug(f"Freezing basket {basket.id}")
            self.freeze_basket(basket)
        else:
            logger.debug(f"Basket {basket.id} is already frozen with status {basket.status}, skipping freeze step")

        # Always keep basket in session for both anonymous and authenticated users
        self.checkout_session.set_submitted_basket(basket)
        # Also store basket ID in a separate session key for anonymous users
        self.request.session['anonymous_basket_id'] = basket.id
        logger.debug(f"Basket {basket.id} saved to session (status: {basket.status})")

        # Handle payment
        error_msg = _(
            "A problem occurred while processing payment for this "
            "order. Please contact customer services if this problem persists"
        )
        pre_payment.send_robust(sender=self, view=self)

        payment_response = None
        try:
            logger.debug(f"Attempting to handle payment with kwargs: {payment_kwargs}")
            payment_response = self.handle_payment(basket, order_number, order_total.incl_tax, **payment_kwargs)
            
            # Skip payment response processing for NeoPay redirects
            # Only return the payment_response for NeoPay if it's a redirect URL, not a success callback
            if payment_method.startswith(PaymentForm.PAYMENT_METHOD_NEOPAY) and isinstance(payment_response, HttpResponseRedirect):
                logger.debug(f"Returning NeoPay redirect URL: {payment_response.url}")
                return payment_response
                
            logger.debug(f"Payment response: {payment_response}")
        except Exception as e:
            msg = str(e)
            logger.exception("Order #%s: payment error (%s)", order_number, msg)
            # Do not automatically restore basket - let the user explicitly request it
            messages.error(self.request, error_msg)
            url = self.request.META.get("HTTP_REFERER", self.request.path)
            return HttpResponseRedirect(url)

        post_payment.send_robust(sender=self, view=self)

        # Handle redirect payments (PayPal, etc)
        if payment_response and payment_method not in self.no_redirect_payments:
            return payment_response

        # If payment successful, try to place order
        logger.info("Order #%s: payment successful, placing order", order_number)

        try:
            return self.handle_order_placement(
                order_number,
                user,
                basket,
                shipping_address,
                shipping_method,
                shipping_charge,
                billing_address,
                order_total,
                **order_kwargs,
            )
        except UnableToPlaceOrder as e:
            # Enhanced error logging for payment success but order creation failure
            error_data = {
                'order_number': order_number,
                'basket_id': basket.id,
                'basket_total': str(basket.total_incl_tax),
                'basket_lines': [{
                    'product': line.product.get_title(),
                    'quantity': line.quantity,
                    'line_price': str(line.line_price_incl_tax),
                } for line in basket.all_lines()],
                'user_id': user.id if user and user.is_authenticated else None,
                'user_email': user.email if user and user.is_authenticated else self.checkout_session.get_guest_email(),
                'shipping_address': shipping_address.summary if shipping_address else None,
                'billing_address': billing_address.summary if billing_address else None,
                'payment_method': payment_kwargs.get('payment_method') if payment_kwargs else None,
                'error_message': str(e)
            }
            
            # Format product lines separately for better readability
            product_lines = []
            for item in error_data['basket_lines']:
                product_lines.append(f'{item["product"]} (x{item["quantity"]})')
            products_text = ', '.join(product_lines)
            
            error_msg_detailed = (
                f"CRITICAL: Payment successful but order creation failed!\n"
                f"Order #{order_number} | Basket #{basket.id}\n"
                f"Error: {str(e)}\n"
                f"Customer: {error_data['user_email']}\n"
                f"Total: {error_data['basket_total']}\n"
                f"Payment Method: {error_data['payment_method']}\n"
                f"Products: {products_text}\n"
                f"Shipping Address: {error_data['shipping_address']}\n"
                f"Billing Address: {error_data['billing_address']}"
            )
            
            logger.critical(error_msg_detailed, extra={'error_data': error_data})
            
            # Do NOT restore frozen basket here as payment was successful
            messages.error(self.request, error_msg)
            url = self.request.META.get("HTTP_REFERER", self.request.path)
            return HttpResponseRedirect(url)

    def handle_payment(self, basket, order_number, total_incl_tax, **kwargs):
        logger.info(f"Processing payment for order {order_number}")
        logger.debug(f"Payment kwargs: {kwargs}")

        shipping_address = kwargs["shipping_address"]
        billing_address = kwargs["billing_address"]
        payment_method = kwargs["payment_method"]

        if settings.DEBUG:
            site = Site(domain=self.request.get_host())
        else:
            site = Site.objects.get(domain=self.request.get_host())

        # Validate payment method
        if not payment_method:
            logger.error("No payment method specified")
            raise Exception("No payment method specified")

        # Validate shipping address
        if not shipping_address:
            logger.error("No shipping address specified")
            raise Exception("No shipping address specified")

        # Use shipping address as billing if not provided
        if not billing_address:
            logger.info("Using shipping address as billing address")
            billing_address = shipping_address

        shipping_method = self.get_shipping_method(basket, shipping_address)
        shipping_charge = shipping_method.calculate(basket)

        customer_ip, is_routable = get_client_ip(self.request)

        basket.order_payment_method = payment_method
        basket.save()

        payment_source_name = payment_method
        payment_source_type = payment_method
        vendor_profile = basket.get_vendor().account
        customer_email = ""
        if self.request.user.is_authenticated:
            customer_email = self.request.user.email
        else:
            customer_email = self.checkout_session.get_guest_email()

        secure = "s" if self.request.is_secure() else ""
        shop_settings = ShopSettings.objects.all()[0]
        user_currency = basket.get_user_currency()
        response = None

        logger.debug(f"Processing payment for order {order_number} with method {payment_method}")

        try:
            if payment_method == PaymentForm.PAYMENT_METHOD_CASH:
                # For cash payments, we just need to create the source
                source_type, __ = models.SourceType.objects.get_or_create(name=payment_method, code=payment_method)
                source = models.Source(
                    source_type=source_type, amount_allocated=total_incl_tax, reference=order_number
                )
                self.add_payment_source(source)
                self.add_payment_event("Pending", total_incl_tax)

            elif payment_method.startswith(PaymentForm.PAYMENT_METHOD_WTP):
                payment = payment_method.rpartition("__")[-1]
                form = WebToPaymentForm(
                    dict(
                        projectid=settings.WEB_TO_PAY_ID,
                        orderid=order_number,
                        accepturl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-success")),
                        cancelurl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-cancel")),
                        callbackurl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-makro")),
                        lang=settings.WEB_TO_PAY_LANGUAGES.get(get_language()[:2], "ENG"),
                        payment=payment,
                        currency=user_currency["currency"],
                        paytext=_("Payment for products"),
                        p_firstname="",
                        p_lastname="",
                        p_email=customer_email,
                        amount=int((total_incl_tax * D(user_currency["rate"])).quantize(D("0.00")) * 100),
                        test=int(shop_settings.test_payments),
                    ),
                    button_html="<input type='submit' value='Pay!'/>",
                    password=settings.WEB_TO_PAY_PASSWORD,
                )
                template = loader.get_template("webtopay/pay.html")
                ctx = {"form": form}
                response = HttpResponse(template.render(ctx, self.request))

            elif payment_method in ["swedbank", PaymentForm.PAYMENT_METHOD_EVERYPAY]:
                everypay = EveryPayHelper()
                payment_data = {
                    "amount": float(total_incl_tax),
                    "customer_url": "http{}://{}{}".format(secure, site.domain, reverse("everypay:return")),
                    "order_reference": order_number,
                    "nonce": timezone.now().timestamp(),
                    "email": customer_email,
                    "customer_ip": customer_ip,
                    "billing_city": billing_address.line4,
                    "billing_country": billing_address.country_id,
                    "billing_line1": billing_address.line1,
                    "billing_postcode": billing_address.postcode,
                    "shipping_city": shipping_address.line4,
                    "shipping_country": shipping_address.country_id,
                    "shipping_line1": shipping_address.line1,
                    "shipping_postcode": shipping_address.postcode,
                    "locale": (self.request.branch.branch if self.request.branch.branch != "eu" else "en"),
                    "timestamp": timezone.now().isoformat(),
                }

                redirect_url, error = everypay.initiate_payment(payment_data)

                if redirect_url:
                    # Store basket info before redirecting
                    self.checkout_session.set_submitted_basket(basket)
                    self.request.session['anonymous_basket_id'] = basket.id
                    return HttpResponseRedirect(redirect_url)
                else:
                    raise Exception("Payment error {}".format(error))

            elif payment_method.startswith(PaymentForm.PAYMENT_METHOD_PAYPAL):
                # Double-check that basket is saved to session
                if not self.checkout_session.get_submitted_basket_id():
                    self.checkout_session.set_submitted_basket(basket)
                    # Also store basket ID in a separate session key for anonymous users
                    self.request.session['anonymous_basket_id'] = basket.id
                    logger.debug(f"Saved basket {basket.id} to session")
                else:
                    logger.debug(f"Basket already in session: {self.checkout_session.get_submitted_basket_id()}")

                logger.info(f"Redirecting to PayPal checkout for order {order_number}")
                return HttpResponseRedirect(reverse("paypal_checkout:checkout"))
                
            elif payment_method.startswith(PaymentForm.PAYMENT_METHOD_NEOPAY):
                # Save basket to session before redirecting
                if not self.checkout_session.get_submitted_basket_id():
                    self.checkout_session.set_submitted_basket(basket)
                    self.request.session['anonymous_basket_id'] = basket.id
                    logger.debug(f"Saved basket {basket.id} to session for NeoPay")
                
                # Use the NeoPayHelper to generate the payment URL
                from project.neopay.helpers import NeoPayHelper
                
                # Generate payment URL and redirect to NeoPay
                payment_url = NeoPayHelper.process_payment(
                    request=self.request,
                    order_number=order_number,
                    total_incl_tax=total_incl_tax,
                    payment_method=payment_method,
                    site=site,
                    user_currency=user_currency,
                    secure=secure
                )
                
                return HttpResponseRedirect(payment_url)
                
            else:
                response = True

        except Exception as e:
            logger.exception(f"Payment error for order {order_number}: {str(e)}")
            # Restore basket on payment error
            self.restore_frozen_basket()
            self.request.session['payment_error'] = str(e)
            raise

        if response and payment_method in self.no_redirect_payments:
            # Payment successful! Record payment source
            try:
                source_type, __ = models.SourceType.objects.get_or_create(
                    name=payment_source_name, code=payment_source_type
                )
                source = models.Source(
                    source_type=source_type,
                    amount_allocated=total_incl_tax,
                    reference=order_number,  # Add reference for tracking
                )
                self.add_payment_source(source)
                self.add_payment_event("Authorised", total_incl_tax)
            except Exception as e:
                logger.error(f"Error recording payment source for order {order_number}: {str(e)}")
                # Do not automatically restore basket on payment error - let user explicitly request it
                raise

        return response

    def get_template_names(self):
        return [self.template_name_preview] if self.preview else [self.template_name]

    def restore_frozen_basket(self):
        """Helper method to restore basket in session after payment failure"""
        basket_id = self.checkout_session.get_submitted_basket_id()
        if basket_id:
            try:
                basket = Basket.objects.get(id=basket_id)
                if basket.status == Basket.FROZEN:
                    basket.thaw()  # Unfreeze the basket
                    logger.debug(f"Thawed basket {basket.id}")
                self.request.basket = basket  # Update request basket
                self.checkout_session.set_submitted_basket(basket)
                logger.debug(f"Restored basket {basket.id} to session")
            except Basket.DoesNotExist:
                logger.warning(f"Could not restore basket {basket_id} - not found")
                pass
