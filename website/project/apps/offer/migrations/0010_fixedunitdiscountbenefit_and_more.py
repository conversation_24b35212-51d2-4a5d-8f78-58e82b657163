# Generated by Django 4.2.9 on 2024-06-21 15:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('offer', '0009_auto_20220901_1751'),
    ]

    operations = [
        migrations.CreateModel(
            name='FixedUnitDiscountBenefit',
            fields=[
            ],
            options={
                'verbose_name': 'Fixed unit discount benefit',
                'verbose_name_plural': 'Fixed unit discount benefits',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('offer.absolutediscountbenefit',),
        ),
        migrations.AddField(
            model_name='rangeproductfileupload',
            name='upload_type',
            field=models.CharField(choices=[('included', 'Included products upload'), ('excluded', 'Excluded products upload')], default='included', max_length=8),
        ),
        migrations.AlterField(
            model_name='benefit',
            name='type',
            field=models.CharField(blank=True, choices=[('Percentage', "Discount is a percentage off of the product's value"), ('Absolute', "Discount is a fixed amount off of the basket's total"), ('Fixed', "Discount is a fixed amount off of the product's value"), ('Multibuy', 'Discount is to give the cheapest product for free'), ('Fixed price', 'Get the products that meet the condition for a fixed price'), ('Shipping absolute', 'Discount is a fixed amount of the shipping cost'), ('Shipping fixed price', 'Get shipping for a fixed price'), ('Shipping percentage', 'Discount is a percentage off of the shipping cost')], max_length=128, verbose_name='Type'),
        ),
        migrations.AlterField(
            model_name='range',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
    ]
