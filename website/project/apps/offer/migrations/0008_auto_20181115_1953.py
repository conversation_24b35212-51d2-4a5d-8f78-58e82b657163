# Generated by Django 2.0.7 on 2018-11-15 19:53

from django.db import migrations, models
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('offer', '0007_conditionaloffer_exclusive'),
    ]

    operations = [
        migrations.AlterField(
            model_name='conditionaloffer',
            name='priority',
            field=models.IntegerField(
                db_index=True,
                default=0,
                help_text='The highest priority offers are applied first',
                verbose_name='Priority',
            ),
        ),
        migrations.CreateModel(
            name='RangeProductFileUpload',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filepath', models.CharField(max_length=255, verbose_name='File Path')),
                ('size', models.PositiveIntegerField(verbose_name='Size')),
                ('date_uploaded', models.DateTimeField(auto_now_add=True, verbose_name='Date Uploaded')),
                (
                    'status',
                    models.Char<PERSON>ield(
                        default='Pending',
                        max_length=32,
                        verbose_name='Status',
                        choices=[('Pending', 'Pending'), ('Failed', 'Failed'), ('Processed', 'Processed')],
                    ),
                ),
                ('error_message', models.CharField(max_length=255, verbose_name='Error Message', blank=True)),
                ('date_processed', models.DateTimeField(verbose_name='Date Processed', null=True)),
                ('num_new_skus', models.PositiveIntegerField(verbose_name='Number of New SKUs', null=True)),
                ('num_unknown_skus', models.PositiveIntegerField(verbose_name='Number of Unknown SKUs', null=True)),
                (
                    'num_duplicate_skus',
                    models.PositiveIntegerField(verbose_name='Number of Duplicate SKUs', null=True),
                ),
                (
                    'range',
                    models.ForeignKey(
                        verbose_name='Range', related_name='file_uploads', to='offer.Range', on_delete=models.CASCADE
                    ),
                ),
                (
                    'uploaded_by',
                    models.ForeignKey(
                        verbose_name='Uploaded By', to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE
                    ),
                ),
            ],
            options={
                'ordering': ('-date_uploaded',),
                'verbose_name_plural': 'Range Product Uploaded Files',
                'verbose_name': 'Range Product Uploaded File',
            },
            bases=(models.Model,),
        ),
        migrations.AlterField(
            model_name='rangeproductfileupload',
            name='date_uploaded',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Date Uploaded'),
        ),
    ]
