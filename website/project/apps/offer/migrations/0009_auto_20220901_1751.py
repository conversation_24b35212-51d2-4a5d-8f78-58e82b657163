# Generated by Django 3.2.15 on 2022-09-01 14:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('offer', '0008_auto_20181115_1953'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='range',
            options={'ordering': ['name'], 'verbose_name': 'Range', 'verbose_name_plural': 'Ranges'},
        ),
        migrations.AddField(
            model_name='conditionaloffer',
            name='combinations',
            field=models.ManyToManyField(blank=True, help_text='Select other non-exclusive offers that this offer can be combined with on the same items', limit_choices_to={'exclusive': False}, related_name='in_combination', to='offer.ConditionalOffer'),
        ),
    ]
